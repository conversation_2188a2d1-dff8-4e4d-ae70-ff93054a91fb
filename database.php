<?php
// إعداد قاعدة البيانات المحلية
class Database {
    private $db;

    public function __construct() {
        try {
            $this->db = new PDO('sqlite:social_services.db');
            $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->createTables();
        } catch(PDOException $e) {
            die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
    }

    public function getConnection() {
        return $this->db;
    }

    private function createTables() {
        // جدول المستخدمين
        $this->db->exec("CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            role TEXT DEFAULT 'employee',
            full_name TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )");

        // جدول الموظفين
        $this->db->exec("CREATE TABLE IF NOT EXISTS employees (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            registration_number TEXT UNIQUE NOT NULL,
            full_name TEXT NOT NULL,
            rank_position TEXT NOT NULL,
            gender TEXT NOT NULL,
            marital_status TEXT NOT NULL,
            children_count INTEGER DEFAULT 0,
            children_names TEXT,
            children_levels TEXT,
            salary DECIMAL(10,2) NOT NULL,
            bank_account TEXT,
            hire_date DATE NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )");

        // جدول الإيرادات
        $this->db->exec("CREATE TABLE IF NOT EXISTS revenues (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            source_type TEXT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            description TEXT,
            date DATE NOT NULL,
            created_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users(id)
        )");

        // جدول المصروفات
        $this->db->exec("CREATE TABLE IF NOT EXISTS expenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            category TEXT NOT NULL,
            subcategory TEXT,
            employee_id INTEGER,
            amount DECIMAL(10,2) NOT NULL,
            description TEXT,
            status TEXT DEFAULT 'قيد الدراسة',
            attachments TEXT,
            date DATE NOT NULL,
            created_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )");

        // جدول السلفات
        $this->db->exec("CREATE TABLE IF NOT EXISTS loans (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            loan_type TEXT NOT NULL,
            reason TEXT NOT NULL,
            monthly_salary DECIMAL(10,2) NOT NULL,
            installments_count INTEGER NOT NULL,
            monthly_installment DECIMAL(10,2) NOT NULL,
            start_date DATE NOT NULL,
            status TEXT DEFAULT 'قيد الدراسة',
            attachments TEXT,
            created_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )");

        // جدول أقساط السلفات
        $this->db->exec("CREATE TABLE IF NOT EXISTS loan_installments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            loan_id INTEGER NOT NULL,
            installment_number INTEGER NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            due_date DATE NOT NULL,
            paid_date DATE,
            status TEXT DEFAULT 'غير مدفوع',
            FOREIGN KEY (loan_id) REFERENCES loans(id)
        )");

        // جدول العمرة
        $this->db->exec("CREATE TABLE IF NOT EXISTS umrah (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            previous_umrah_count INTEGER DEFAULT 0,
            has_companion BOOLEAN DEFAULT 0,
            companion_name TEXT,
            total_cost DECIMAL(10,2),
            committee_contribution DECIMAL(10,2),
            employee_contribution DECIMAL(10,2),
            status TEXT DEFAULT 'قيد الدراسة',
            travel_agency TEXT,
            attachments TEXT,
            created_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )");

        // جدول الاصطياف
        $this->db->exec("CREATE TABLE IF NOT EXISTS vacation (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            family_members_count INTEGER NOT NULL,
            children_participating INTEGER DEFAULT 0,
            destination TEXT,
            duration_days INTEGER,
            total_cost DECIMAL(10,2),
            committee_contribution DECIMAL(10,2),
            employee_contribution DECIMAL(10,2),
            status TEXT DEFAULT 'قيد الدراسة',
            travel_date DATE,
            attachments TEXT,
            created_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )");

        // إنشاء مستخدم افتراضي
        $this->createDefaultUser();

        // إضافة بيانات تجريبية
        $this->createSampleData();
    }

    private function createDefaultUser() {
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
        $stmt->execute();

        if ($stmt->fetchColumn() == 0) {
            $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $this->db->prepare("INSERT INTO users (username, password, role, full_name) VALUES (?, ?, ?, ?)");
            $stmt->execute(['admin', $hashedPassword, 'admin', 'مدير النظام']);
        }
    }

    private function createSampleData() {
        // إضافة بيانات تجريبية للموظفين
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM employees");
        $stmt->execute();
        $employeeCount = $stmt->fetchColumn();

        if ($employeeCount == 0) {
            $sampleEmployees = [
                ['EMP001', 'أحمد محمد علي', 'مدير', 'ذكر', 'متزوج', 3, 'محمد، فاطمة، عبد الله', 'ابتدائي، متوسط، ثانوي', 80000.00, '2020-01-15'],
                ['EMP002', 'فاطمة الزهراء بن علي', 'رئيس مصلحة', 'أنثى', 'متزوج', 2, 'سارة، يوسف', 'ابتدائي، متوسط', 65000.00, '2019-03-10'],
                ['EMP003', 'محمد الأمين قاسم', 'موظف', 'ذكر', 'أعزب', 0, '', '', 45000.00, '2021-06-20'],
                ['EMP004', 'خديجة بوعلام', 'موظف', 'أنثى', 'متزوج', 1, 'أمينة', 'ابتدائي', 42000.00, '2022-02-14'],
                ['EMP005', 'عبد الرحمن سليماني', 'عون', 'ذكر', 'متزوج', 4, 'عمر، زينب، حسام، ليلى', 'ثانوي، متوسط، ابتدائي، روضة', 38000.00, '2018-09-05']
            ];

            $stmt = $this->db->prepare("INSERT INTO employees (registration_number, full_name, rank_position, gender, marital_status, children_count, children_names, children_levels, salary, hire_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

            foreach ($sampleEmployees as $employee) {
                $stmt->execute($employee);
            }
        }

        // إضافة بيانات تجريبية للإيرادات
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM revenues");
        $stmt->execute();
        $revenueCount = $stmt->fetchColumn();

        if ($revenueCount == 0) {
            $sampleRevenues = [
                ['إعانة الدولة', 2000000.00, 'إعانة شهرية من الدولة', '2024-01-01'],
                ['فوائد بنكية', 15000.00, 'فوائد الحساب البنكي', '2024-01-15'],
                ['تبرعات', 50000.00, 'تبرعات من الموظفين', '2024-02-01'],
                ['إعانة الدولة', 2000000.00, 'إعانة شهرية من الدولة', '2024-02-01'],
                ['دعم خارجي', 100000.00, 'دعم من منظمة خيرية', '2024-02-20']
            ];

            $stmt = $this->db->prepare("INSERT INTO revenues (source_type, amount, description, date, created_by) VALUES (?, ?, ?, ?, 1)");

            foreach ($sampleRevenues as $revenue) {
                $stmt->execute($revenue);
            }
        }

        // إضافة بيانات تجريبية للمصروفات
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM expenses");
        $stmt->execute();
        $expenseCount = $stmt->fetchColumn();

        if ($expenseCount == 0) {
            $sampleExpenses = [
                ['المساعدات الاجتماعية', 'الوفاة', 1, 50000.00, 'مساعدة وفاة والد الموظف', '2024-01-10'],
                ['المساعدات الاجتماعية', 'الزواج', 2, 30000.00, 'مساعدة زواج الموظفة', '2024-01-20'],
                ['المنح الدراسية', 'منحة التفوق', 3, 20000.00, 'منحة تفوق للطفل', '2024-02-05'],
                ['الصحة', 'استرجاع تكاليف طبية', 4, 15000.00, 'استرجاع تكاليف عملية جراحية', '2024-02-15'],
                ['المساعدات الاجتماعية', 'الختان', 5, 10000.00, 'مساعدة ختان الطفل', '2024-02-25']
            ];

            $stmt = $this->db->prepare("INSERT INTO expenses (category, subcategory, employee_id, amount, description, date, created_by) VALUES (?, ?, ?, ?, ?, ?, 1)");

            foreach ($sampleExpenses as $expense) {
                $stmt->execute($expense);
            }
        }
    }
}

// إنشاء اتصال قاعدة البيانات
$database = new Database();
$pdo = $database->getConnection();
?>
