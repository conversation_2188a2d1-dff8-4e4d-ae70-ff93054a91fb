<?php
// إعداد قاعدة البيانات المحلية
class Database {
    private $db;
    
    public function __construct() {
        try {
            $this->db = new PDO('sqlite:social_services.db');
            $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->createTables();
        } catch(PDOException $e) {
            die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
    }
    
    public function getConnection() {
        return $this->db;
    }
    
    private function createTables() {
        // جدول المستخدمين
        $this->db->exec("CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            role TEXT DEFAULT 'employee',
            full_name TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )");
        
        // جدول الموظفين
        $this->db->exec("CREATE TABLE IF NOT EXISTS employees (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            registration_number TEXT UNIQUE NOT NULL,
            full_name TEXT NOT NULL,
            rank_position TEXT NOT NULL,
            gender TEXT NOT NULL,
            marital_status TEXT NOT NULL,
            children_count INTEGER DEFAULT 0,
            children_names TEXT,
            children_levels TEXT,
            salary DECIMAL(10,2) NOT NULL,
            bank_account TEXT,
            hire_date DATE NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )");
        
        // جدول الإيرادات
        $this->db->exec("CREATE TABLE IF NOT EXISTS revenues (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            source_type TEXT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            description TEXT,
            date DATE NOT NULL,
            created_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users(id)
        )");
        
        // جدول المصروفات
        $this->db->exec("CREATE TABLE IF NOT EXISTS expenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            category TEXT NOT NULL,
            subcategory TEXT,
            employee_id INTEGER,
            amount DECIMAL(10,2) NOT NULL,
            description TEXT,
            status TEXT DEFAULT 'قيد الدراسة',
            attachments TEXT,
            date DATE NOT NULL,
            created_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )");
        
        // جدول السلفات
        $this->db->exec("CREATE TABLE IF NOT EXISTS loans (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            loan_type TEXT NOT NULL,
            reason TEXT NOT NULL,
            monthly_salary DECIMAL(10,2) NOT NULL,
            installments_count INTEGER NOT NULL,
            monthly_installment DECIMAL(10,2) NOT NULL,
            start_date DATE NOT NULL,
            status TEXT DEFAULT 'قيد الدراسة',
            attachments TEXT,
            created_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )");
        
        // جدول أقساط السلفات
        $this->db->exec("CREATE TABLE IF NOT EXISTS loan_installments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            loan_id INTEGER NOT NULL,
            installment_number INTEGER NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            due_date DATE NOT NULL,
            paid_date DATE,
            status TEXT DEFAULT 'غير مدفوع',
            FOREIGN KEY (loan_id) REFERENCES loans(id)
        )");
        
        // جدول العمرة
        $this->db->exec("CREATE TABLE IF NOT EXISTS umrah (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            previous_umrah_count INTEGER DEFAULT 0,
            has_companion BOOLEAN DEFAULT 0,
            companion_name TEXT,
            total_cost DECIMAL(10,2),
            committee_contribution DECIMAL(10,2),
            employee_contribution DECIMAL(10,2),
            status TEXT DEFAULT 'قيد الدراسة',
            travel_agency TEXT,
            attachments TEXT,
            created_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )");
        
        // جدول الاصطياف
        $this->db->exec("CREATE TABLE IF NOT EXISTS vacation (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER NOT NULL,
            family_members_count INTEGER NOT NULL,
            children_participating INTEGER DEFAULT 0,
            destination TEXT,
            duration_days INTEGER,
            total_cost DECIMAL(10,2),
            committee_contribution DECIMAL(10,2),
            employee_contribution DECIMAL(10,2),
            status TEXT DEFAULT 'قيد الدراسة',
            travel_date DATE,
            attachments TEXT,
            created_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES employees(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )");
        
        // إنشاء مستخدم افتراضي
        $this->createDefaultUser();
    }
    
    private function createDefaultUser() {
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
        $stmt->execute();
        
        if ($stmt->fetchColumn() == 0) {
            $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $this->db->prepare("INSERT INTO users (username, password, role, full_name) VALUES (?, ?, ?, ?)");
            $stmt->execute(['admin', $hashedPassword, 'admin', 'مدير النظام']);
        }
    }
}

// إنشاء اتصال قاعدة البيانات
$database = new Database();
$pdo = $database->getConnection();
?>
