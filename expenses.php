<?php
require_once 'auth.php';

// التحقق من تسجيل الدخول
$auth->requireLogin();

header('Content-Type: application/json; charset=utf-8');

class ExpenseManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    public function getAllExpenses() {
        try {
            $stmt = $this->db->query("
                SELECT e.*, emp.full_name as employee_name, emp.registration_number,
                       u.full_name as created_by_name
                FROM expenses e
                LEFT JOIN employees emp ON e.employee_id = emp.id
                LEFT JOIN users u ON e.created_by = u.id
                ORDER BY e.date DESC, e.created_at DESC
            ");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع بيانات المصروفات: " . $e->getMessage());
        }
    }
    
    public function getExpenseById($id) {
        try {
            $stmt = $this->db->prepare("
                SELECT e.*, emp.full_name as employee_name, emp.registration_number,
                       u.full_name as created_by_name
                FROM expenses e
                LEFT JOIN employees emp ON e.employee_id = emp.id
                LEFT JOIN users u ON e.created_by = u.id
                WHERE e.id = ?
            ");
            $stmt->execute([$id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع بيانات المصروف: " . $e->getMessage());
        }
    }
    
    public function createExpense($data, $userId) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO expenses (
                    category, subcategory, employee_id, amount, description, 
                    date, attachments, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $result = $stmt->execute([
                $data['category'],
                $data['subcategory'] ?? '',
                $data['employee_id'] ?: null,
                $data['amount'],
                $data['description'] ?? '',
                $data['date'],
                $data['attachments'] ?? '',
                $userId
            ]);
            
            if ($result) {
                return $this->db->lastInsertId();
            } else {
                throw new Exception("فشل في إضافة المصروف");
            }
        } catch (PDOException $e) {
            throw new Exception("خطأ في إضافة المصروف: " . $e->getMessage());
        }
    }
    
    public function updateExpense($id, $data) {
        try {
            $stmt = $this->db->prepare("
                UPDATE expenses SET 
                    category = ?, subcategory = ?, employee_id = ?, amount = ?, 
                    description = ?, date = ?, attachments = ?
                WHERE id = ?
            ");
            
            $result = $stmt->execute([
                $data['category'],
                $data['subcategory'] ?? '',
                $data['employee_id'] ?: null,
                $data['amount'],
                $data['description'] ?? '',
                $data['date'],
                $data['attachments'] ?? '',
                $id
            ]);
            
            if (!$result) {
                throw new Exception("فشل في تحديث المصروف");
            }
            
            return true;
        } catch (PDOException $e) {
            throw new Exception("خطأ في تحديث المصروف: " . $e->getMessage());
        }
    }
    
    public function updateExpenseStatus($id, $status) {
        try {
            $stmt = $this->db->prepare("UPDATE expenses SET status = ? WHERE id = ?");
            $result = $stmt->execute([$status, $id]);
            
            if (!$result) {
                throw new Exception("فشل في تحديث حالة المصروف");
            }
            
            return true;
        } catch (PDOException $e) {
            throw new Exception("خطأ في تحديث حالة المصروف: " . $e->getMessage());
        }
    }
    
    public function deleteExpense($id) {
        try {
            $stmt = $this->db->prepare("DELETE FROM expenses WHERE id = ?");
            $result = $stmt->execute([$id]);
            
            if (!$result) {
                throw new Exception("فشل في حذف المصروف");
            }
            
            return true;
        } catch (PDOException $e) {
            throw new Exception("خطأ في حذف المصروف: " . $e->getMessage());
        }
    }
    
    public function getExpenseStatistics() {
        try {
            $stats = [];
            
            // إجمالي المصروفات
            $stmt = $this->db->query("SELECT COALESCE(SUM(amount), 0) as total FROM expenses");
            $stats['total'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // المساعدات الاجتماعية
            $stmt = $this->db->query("
                SELECT COALESCE(SUM(amount), 0) as social_assistance 
                FROM expenses 
                WHERE category = 'المساعدات الاجتماعية'
            ");
            $stats['social_assistance'] = $stmt->fetch(PDO::FETCH_ASSOC)['social_assistance'];
            
            // المنح الدراسية
            $stmt = $this->db->query("
                SELECT COALESCE(SUM(amount), 0) as scholarships 
                FROM expenses 
                WHERE category = 'المنح الدراسية'
            ");
            $stats['scholarships'] = $stmt->fetch(PDO::FETCH_ASSOC)['scholarships'];
            
            // المصروفات الصحية
            $stmt = $this->db->query("
                SELECT COALESCE(SUM(amount), 0) as health 
                FROM expenses 
                WHERE category = 'الصحة'
            ");
            $stats['health'] = $stmt->fetch(PDO::FETCH_ASSOC)['health'];
            
            // المصروفات الشهرية (آخر 12 شهر)
            $monthlyExpenses = [];
            for ($i = 11; $i >= 0; $i--) {
                $month = date('Y-m', strtotime("-$i months"));
                $stmt = $this->db->prepare("
                    SELECT COALESCE(SUM(amount), 0) as total 
                    FROM expenses 
                    WHERE strftime('%Y-%m', date) = ?
                ");
                $stmt->execute([$month]);
                $monthlyExpenses[] = [
                    'month' => $month,
                    'total' => floatval($stmt->fetch(PDO::FETCH_ASSOC)['total'])
                ];
            }
            $stats['monthly_expenses'] = $monthlyExpenses;
            
            // توزيع المصروفات حسب الفئة
            $stmt = $this->db->query("
                SELECT category, COALESCE(SUM(amount), 0) as total, COUNT(*) as count
                FROM expenses 
                GROUP BY category
                ORDER BY total DESC
            ");
            $stats['by_category'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // توزيع المصروفات حسب الحالة
            $stmt = $this->db->query("
                SELECT status, COUNT(*) as count
                FROM expenses 
                GROUP BY status
            ");
            $stats['by_status'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return $stats;
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع الإحصائيات: " . $e->getMessage());
        }
    }
    
    public function getExpensesByCategory($category) {
        try {
            $stmt = $this->db->prepare("
                SELECT e.*, emp.full_name as employee_name, emp.registration_number
                FROM expenses e
                LEFT JOIN employees emp ON e.employee_id = emp.id
                WHERE e.category = ?
                ORDER BY e.date DESC
            ");
            $stmt->execute([$category]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع المصروفات: " . $e->getMessage());
        }
    }
    
    public function getExpensesByDateRange($startDate, $endDate) {
        try {
            $stmt = $this->db->prepare("
                SELECT e.*, emp.full_name as employee_name, emp.registration_number
                FROM expenses e
                LEFT JOIN employees emp ON e.employee_id = emp.id
                WHERE e.date BETWEEN ? AND ?
                ORDER BY e.date DESC
            ");
            $stmt->execute([$startDate, $endDate]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع المصروفات: " . $e->getMessage());
        }
    }
    
    public function getExpensesByEmployee($employeeId) {
        try {
            $stmt = $this->db->prepare("
                SELECT e.*, emp.full_name as employee_name, emp.registration_number
                FROM expenses e
                LEFT JOIN employees emp ON e.employee_id = emp.id
                WHERE e.employee_id = ?
                ORDER BY e.date DESC
            ");
            $stmt->execute([$employeeId]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع المصروفات: " . $e->getMessage());
        }
    }
    
    public function getPendingExpenses() {
        try {
            $stmt = $this->db->query("
                SELECT e.*, emp.full_name as employee_name, emp.registration_number
                FROM expenses e
                LEFT JOIN employees emp ON e.employee_id = emp.id
                WHERE e.status = 'قيد الدراسة'
                ORDER BY e.created_at ASC
            ");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع المصروفات المعلقة: " . $e->getMessage());
        }
    }
}

// معالجة الطلبات
try {
    $expenseManager = new ExpenseManager($pdo);
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    $currentUser = $auth->getCurrentUser();
    
    switch ($action) {
        case 'list':
            $expenses = $expenseManager->getAllExpenses();
            echo json_encode([
                'success' => true,
                'data' => $expenses
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'get':
            $id = $_GET['id'] ?? 0;
            if (!$id) {
                throw new Exception("معرف المصروف مطلوب");
            }
            
            $expense = $expenseManager->getExpenseById($id);
            if (!$expense) {
                throw new Exception("المصروف غير موجود");
            }
            
            echo json_encode([
                'success' => true,
                'data' => $expense
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'create':
            $requiredFields = ['category', 'amount', 'date'];
            
            foreach ($requiredFields as $field) {
                if (empty($_POST[$field])) {
                    throw new Exception("الحقل $field مطلوب");
                }
            }
            
            // التحقق من صحة المبلغ
            if (!is_numeric($_POST['amount']) || $_POST['amount'] <= 0) {
                throw new Exception("المبلغ يجب أن يكون رقماً موجباً");
            }
            
            $expenseId = $expenseManager->createExpense($_POST, $currentUser['id']);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم إضافة المصروف بنجاح',
                'expense_id' => $expenseId
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'update':
            $id = $_POST['id'] ?? 0;
            if (!$id) {
                throw new Exception("معرف المصروف مطلوب");
            }
            
            $expenseManager->updateExpense($id, $_POST);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث المصروف بنجاح'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'update_status':
            $id = $_POST['id'] ?? 0;
            $status = $_POST['status'] ?? '';
            
            if (!$id || !$status) {
                throw new Exception("معرف المصروف والحالة مطلوبان");
            }
            
            $expenseManager->updateExpenseStatus($id, $status);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث حالة المصروف بنجاح'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'delete':
            $id = $_POST['id'] ?? 0;
            if (!$id) {
                throw new Exception("معرف المصروف مطلوب");
            }
            
            $expenseManager->deleteExpense($id);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم حذف المصروف بنجاح'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'statistics':
            $stats = $expenseManager->getExpenseStatistics();
            
            echo json_encode([
                'success' => true,
                'data' => $stats
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'by_category':
            $category = $_GET['category'] ?? '';
            if (!$category) {
                throw new Exception("الفئة مطلوبة");
            }
            
            $expenses = $expenseManager->getExpensesByCategory($category);
            
            echo json_encode([
                'success' => true,
                'data' => $expenses
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'pending':
            $expenses = $expenseManager->getPendingExpenses();
            
            echo json_encode([
                'success' => true,
                'data' => $expenses
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            throw new Exception("إجراء غير صحيح");
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
