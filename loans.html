<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>السلفات المالية - لجنة الخدمات الاجتماعية</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="dashboard">
    <!-- شريط التنقل العلوي -->
    <nav class="navbar">
        <div class="navbar-content">
            <a href="dashboard.html" class="navbar-brand">
                <i class="fas fa-users"></i>
                لجنة الخدمات الاجتماعية - ولاية الجلفة
            </a>
            <div class="navbar-user">
                <div class="user-info">
                    <i class="fas fa-user"></i>
                    <span id="userName">مرحباً</span>
                </div>
                <button class="btn btn-logout" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>
    </nav>

    <!-- الشريط الجانبي -->
    <aside class="sidebar">
        <ul class="sidebar-menu">
            <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i>لوحة التحكم</a></li>
            <li><a href="employees.html"><i class="fas fa-users"></i>إدارة الموظفين</a></li>
            <li><a href="revenues.html"><i class="fas fa-arrow-up"></i>الإيرادات</a></li>
            <li><a href="expenses.html"><i class="fas fa-arrow-down"></i>المصروفات</a></li>
            <li><a href="loans.html" class="active"><i class="fas fa-money-bill-wave"></i>السلفات المالية</a></li>
            <li><a href="umrah.html"><i class="fas fa-kaaba"></i>العمرة</a></li>
            <li><a href="vacation.html"><i class="fas fa-umbrella-beach"></i>الاصطياف</a></li>
            <li><a href="reports.html"><i class="fas fa-chart-bar"></i>التقارير والإحصائيات</a></li>
            <li><a href="settings.html"><i class="fas fa-cog"></i>الإعدادات</a></li>
        </ul>
    </aside>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">إدارة السلفات المالية</h1>
            <p class="page-subtitle">إدارة طلبات السلفات ومتابعة الاسترجاع</p>
        </div>

        <!-- إحصائيات السلفات -->
        <div class="stats-grid" style="margin-bottom: 30px;">
            <div class="stat-card">
                <div class="stat-icon blue">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="stat-number" id="totalLoans">0</div>
                <div class="stat-label">إجمالي السلفات</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon green">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number" id="approvedLoans">0</div>
                <div class="stat-label">السلفات المعتمدة</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon orange">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number" id="pendingLoans">0</div>
                <div class="stat-label">قيد الدراسة</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon purple">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-number currency" id="totalAmount">0</div>
                <div class="stat-label">إجمالي المبالغ</div>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div style="margin-bottom: 20px;">
            <button class="btn btn-primary" onclick="showAddLoanModal()">
                <i class="fas fa-plus"></i>
                طلب سلفة جديدة
            </button>
            <button class="btn btn-success" onclick="exportLoans()">
                <i class="fas fa-download"></i>
                تصدير البيانات
            </button>
            <button class="btn btn-info" onclick="showInstallmentsReport()">
                <i class="fas fa-calendar-alt"></i>
                تقرير الأقساط
            </button>
        </div>

        <!-- البحث والفلترة -->
        <div class="card" style="margin-bottom: 20px;">
            <div class="card-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="searchLoan">البحث</label>
                        <input type="text" id="searchLoan" class="form-control" placeholder="البحث بالاسم أو رقم التسجيل">
                    </div>
                    <div class="form-group">
                        <label for="filterStatus">الحالة</label>
                        <select id="filterStatus" class="form-control">
                            <option value="">جميع الحالات</option>
                            <option value="قيد الدراسة">قيد الدراسة</option>
                            <option value="موافق عليه">موافق عليه</option>
                            <option value="مرفوض">مرفوض</option>
                            <option value="مكتمل">مكتمل</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="filterType">نوع السلفة</label>
                        <select id="filterType" class="form-control">
                            <option value="">جميع الأنواع</option>
                            <option value="عادية">عادية</option>
                            <option value="مستعجلة">مستعجلة</option>
                            <option value="سكن">سكن</option>
                            <option value="علاج">علاج</option>
                            <option value="تعليم">تعليم</option>
                        </select>
                    </div>
                    <div class="form-group" style="display: flex; align-items: end;">
                        <button class="btn btn-info" onclick="filterLoans()">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول السلفات -->
        <div class="table-container">
            <div class="table-header">
                <h3 class="table-title">قائمة السلفات</h3>
                <button class="btn btn-primary btn-sm" onclick="loadLoans()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث
                </button>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>الموظف</th>
                        <th>نوع السلفة</th>
                        <th>المبلغ</th>
                        <th>عدد الأقساط</th>
                        <th>القسط الشهري</th>
                        <th>تاريخ البداية</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="loansTable">
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 40px;">
                            <div class="loading">
                                <div class="spinner"></div>
                                <p>جاري تحميل البيانات...</p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </main>

    <!-- نافذة إضافة/تعديل سلفة -->
    <div id="loanModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="loanModalTitle">طلب سلفة جديدة</h3>
                <button class="btn-close" onclick="closeLoanModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="loanForm">
                    <input type="hidden" id="loanId" name="id">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="employeeSelect">الموظف *</label>
                            <select id="employeeSelect" name="employee_id" class="form-control" required>
                                <option value="">اختر الموظف</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="loanType">نوع السلفة *</label>
                            <select id="loanType" name="loan_type" class="form-control" required>
                                <option value="">اختر النوع</option>
                                <option value="عادية">عادية</option>
                                <option value="مستعجلة">مستعجلة</option>
                                <option value="سكن">سكن</option>
                                <option value="علاج">علاج</option>
                                <option value="تعليم">تعليم</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="loanAmount">المبلغ المطلوب (د.ج) *</label>
                            <input type="number" id="loanAmount" name="amount" class="form-control" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label for="monthlySalary">الراتب الشهري (د.ج) *</label>
                            <input type="number" id="monthlySalary" name="monthly_salary" class="form-control" step="0.01" required readonly>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="installmentsCount">عدد الأقساط *</label>
                            <input type="number" id="installmentsCount" name="installments_count" class="form-control" min="1" max="60" required>
                        </div>
                        <div class="form-group">
                            <label for="monthlyInstallment">القسط الشهري (د.ج)</label>
                            <input type="number" id="monthlyInstallment" name="monthly_installment" class="form-control" step="0.01" readonly>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="loanReason">سبب الطلب *</label>
                        <textarea id="loanReason" name="reason" class="form-control" rows="3" required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="startDate">تاريخ بداية الاسترجاع *</label>
                        <input type="date" id="startDate" name="start_date" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="loanAttachments">المرفقات</label>
                        <input type="file" id="loanAttachments" name="attachments" class="form-control" multiple accept=".pdf,.jpg,.jpeg,.png">
                        <small class="form-text">كشف الراتب، نسخة بطاقة الهوية، وثائق أخرى</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeLoanModal()">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveLoan()">حفظ الطلب</button>
            </div>
        </div>
    </div>

    <!-- نافذة تفاصيل السلفة -->
    <div id="loanDetailsModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تفاصيل السلفة</h3>
                <button class="btn-close" onclick="closeLoanDetailsModal()">&times;</button>
            </div>
            <div class="modal-body" id="loanDetailsContent">
                <!-- سيتم ملء المحتوى ديناميكياً -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeLoanDetailsModal()">إغلاق</button>
                <button type="button" class="btn btn-success" onclick="approveLoan()" id="approveLoanBtn" style="display: none;">موافقة</button>
                <button type="button" class="btn btn-danger" onclick="rejectLoan()" id="rejectLoanBtn" style="display: none;">رفض</button>
            </div>
        </div>
    </div>

    <script>
        let loans = [];
        let employees = [];
        let currentLoan = null;

        // تحميل البيانات عند تحميل الصفحة
        window.addEventListener('load', function() {
            checkAuthentication();
            loadEmployees();
            loadLoans();
            loadStatistics();
        });

        // التحقق من المصادقة
        async function checkAuthentication() {
            try {
                const response = await fetch('auth.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=get_current_user'
                });
                
                const result = await response.json();
                
                if (result.success && result.user) {
                    document.getElementById('userName').textContent = `مرحباً، ${result.user.full_name}`;
                } else {
                    window.location.href = 'index.html';
                }
            } catch (error) {
                console.error('خطأ في التحقق من المصادقة:', error);
                window.location.href = 'index.html';
            }
        }

        // تحميل قائمة الموظفين
        async function loadEmployees() {
            try {
                const response = await fetch('employees.php?action=list');
                const result = await response.json();
                
                if (result.success) {
                    employees = result.data;
                    populateEmployeeSelect();
                }
            } catch (error) {
                console.error('خطأ في تحميل الموظفين:', error);
            }
        }

        // ملء قائمة الموظفين
        function populateEmployeeSelect() {
            const select = document.getElementById('employeeSelect');
            select.innerHTML = '<option value="">اختر الموظف</option>';
            
            employees.forEach(employee => {
                const option = document.createElement('option');
                option.value = employee.id;
                option.textContent = `${employee.full_name} - ${employee.registration_number}`;
                option.dataset.salary = employee.salary;
                select.appendChild(option);
            });
        }

        // تحميل قائمة السلفات
        async function loadLoans() {
            try {
                const response = await fetch('loans.php?action=list');
                const result = await response.json();
                
                if (result.success) {
                    loans = result.data;
                    displayLoans(loans);
                } else {
                    showAlert('خطأ في تحميل البيانات: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('خطأ في تحميل السلفات:', error);
                showAlert('حدث خطأ في تحميل البيانات', 'danger');
            }
        }

        // تحميل الإحصائيات
        async function loadStatistics() {
            try {
                const response = await fetch('loans.php?action=statistics');
                const result = await response.json();
                
                if (result.success) {
                    const stats = result.data;
                    document.getElementById('totalLoans').textContent = stats.total || 0;
                    document.getElementById('approvedLoans').textContent = stats.approved || 0;
                    document.getElementById('pendingLoans').textContent = stats.pending || 0;
                    document.getElementById('totalAmount').textContent = formatCurrency(stats.total_amount || 0);
                }
            } catch (error) {
                console.error('خطأ في تحميل الإحصائيات:', error);
            }
        }

        // عرض السلفات في الجدول
        function displayLoans(loansList) {
            const tableBody = document.getElementById('loansTable');
            
            if (loansList.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="8" style="text-align: center; padding: 40px;">لا توجد سلفات مسجلة</td></tr>';
                return;
            }
            
            tableBody.innerHTML = loansList.map(loan => `
                <tr>
                    <td>${loan.employee_name || 'غير محدد'}</td>
                    <td>${loan.loan_type}</td>
                    <td class="currency">${formatCurrency(loan.amount)}</td>
                    <td>${loan.installments_count}</td>
                    <td class="currency">${formatCurrency(loan.monthly_installment)}</td>
                    <td>${formatDate(loan.start_date)}</td>
                    <td><span class="status status-${getStatusClass(loan.status)}">${loan.status}</span></td>
                    <td>
                        <button class="btn btn-info btn-sm" onclick="viewLoanDetails(${loan.id})" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-warning btn-sm" onclick="editLoan(${loan.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-success btn-sm" onclick="viewInstallments(${loan.id})" title="الأقساط">
                            <i class="fas fa-calendar-alt"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // فلترة السلفات
        function filterLoans() {
            const searchTerm = document.getElementById('searchLoan').value.toLowerCase();
            const statusFilter = document.getElementById('filterStatus').value;
            const typeFilter = document.getElementById('filterType').value;
            
            const filteredLoans = loans.filter(loan => {
                const matchesSearch = (loan.employee_name || '').toLowerCase().includes(searchTerm) || 
                                    (loan.registration_number || '').toLowerCase().includes(searchTerm);
                const matchesStatus = !statusFilter || loan.status === statusFilter;
                const matchesType = !typeFilter || loan.loan_type === typeFilter;
                
                return matchesSearch && matchesStatus && matchesType;
            });
            
            displayLoans(filteredLoans);
        }

        // إضافة مستمعين للبحث المباشر
        document.getElementById('searchLoan').addEventListener('input', filterLoans);
        document.getElementById('filterStatus').addEventListener('change', filterLoans);
        document.getElementById('filterType').addEventListener('change', filterLoans);

        // عرض نافذة إضافة سلفة
        function showAddLoanModal() {
            currentLoan = null;
            document.getElementById('loanModalTitle').textContent = 'طلب سلفة جديدة';
            document.getElementById('loanForm').reset();
            document.getElementById('loanId').value = '';
            document.getElementById('loanModal').style.display = 'flex';
        }

        // حساب القسط الشهري تلقائياً
        document.getElementById('loanAmount').addEventListener('input', calculateInstallment);
        document.getElementById('installmentsCount').addEventListener('input', calculateInstallment);

        function calculateInstallment() {
            const amount = parseFloat(document.getElementById('loanAmount').value) || 0;
            const installments = parseInt(document.getElementById('installmentsCount').value) || 1;
            
            if (amount > 0 && installments > 0) {
                const monthlyInstallment = amount / installments;
                document.getElementById('monthlyInstallment').value = monthlyInstallment.toFixed(2);
            }
        }

        // تحديث الراتب عند اختيار الموظف
        document.getElementById('employeeSelect').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const salary = selectedOption.dataset.salary || 0;
            document.getElementById('monthlySalary').value = salary;
        });

        // حفظ السلفة
        async function saveLoan() {
            const form = document.getElementById('loanForm');
            const formData = new FormData(form);
            
            const action = document.getElementById('loanId').value ? 'update' : 'create';
            formData.append('action', action);
            
            try {
                const response = await fetch('loans.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert(result.message, 'success');
                    closeLoanModal();
                    loadLoans();
                    loadStatistics();
                } else {
                    showAlert('خطأ: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('خطأ في حفظ السلفة:', error);
                showAlert('حدث خطأ في حفظ البيانات', 'danger');
            }
        }

        // عرض تفاصيل السلفة
        async function viewLoanDetails(id) {
            try {
                const response = await fetch(`loans.php?action=get&id=${id}`);
                const result = await response.json();
                
                if (result.success) {
                    const loan = result.data;
                    displayLoanDetails(loan);
                } else {
                    showAlert('خطأ في تحميل التفاصيل', 'danger');
                }
            } catch (error) {
                console.error('خطأ في تحميل تفاصيل السلفة:', error);
                showAlert('حدث خطأ في تحميل التفاصيل', 'danger');
            }
        }

        // عرض تفاصيل السلفة في النافذة
        function displayLoanDetails(loan) {
            const content = document.getElementById('loanDetailsContent');
            content.innerHTML = `
                <div class="loan-details">
                    <div class="detail-row">
                        <strong>الموظف:</strong> ${loan.employee_name}
                    </div>
                    <div class="detail-row">
                        <strong>نوع السلفة:</strong> ${loan.loan_type}
                    </div>
                    <div class="detail-row">
                        <strong>المبلغ:</strong> <span class="currency">${formatCurrency(loan.amount)}</span>
                    </div>
                    <div class="detail-row">
                        <strong>الراتب الشهري:</strong> <span class="currency">${formatCurrency(loan.monthly_salary)}</span>
                    </div>
                    <div class="detail-row">
                        <strong>عدد الأقساط:</strong> ${loan.installments_count}
                    </div>
                    <div class="detail-row">
                        <strong>القسط الشهري:</strong> <span class="currency">${formatCurrency(loan.monthly_installment)}</span>
                    </div>
                    <div class="detail-row">
                        <strong>تاريخ البداية:</strong> ${formatDate(loan.start_date)}
                    </div>
                    <div class="detail-row">
                        <strong>الحالة:</strong> <span class="status status-${getStatusClass(loan.status)}">${loan.status}</span>
                    </div>
                    <div class="detail-row">
                        <strong>سبب الطلب:</strong> ${loan.reason}
                    </div>
                    <div class="detail-row">
                        <strong>تاريخ الطلب:</strong> ${formatDate(loan.created_at)}
                    </div>
                </div>
            `;
            
            // إظهار أزرار الموافقة/الرفض للطلبات المعلقة
            const approveBtn = document.getElementById('approveLoanBtn');
            const rejectBtn = document.getElementById('rejectLoanBtn');
            
            if (loan.status === 'قيد الدراسة') {
                approveBtn.style.display = 'inline-block';
                rejectBtn.style.display = 'inline-block';
                approveBtn.onclick = () => updateLoanStatus(loan.id, 'موافق عليه');
                rejectBtn.onclick = () => updateLoanStatus(loan.id, 'مرفوض');
            } else {
                approveBtn.style.display = 'none';
                rejectBtn.style.display = 'none';
            }
            
            document.getElementById('loanDetailsModal').style.display = 'flex';
        }

        // تحديث حالة السلفة
        async function updateLoanStatus(id, status) {
            try {
                const formData = new FormData();
                formData.append('action', 'update_status');
                formData.append('id', id);
                formData.append('status', status);
                
                const response = await fetch('loans.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert(result.message, 'success');
                    closeLoanDetailsModal();
                    loadLoans();
                    loadStatistics();
                } else {
                    showAlert('خطأ: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('خطأ في تحديث الحالة:', error);
                showAlert('حدث خطأ في تحديث الحالة', 'danger');
            }
        }

        // إغلاق النوافذ المنبثقة
        function closeLoanModal() {
            document.getElementById('loanModal').style.display = 'none';
        }

        function closeLoanDetailsModal() {
            document.getElementById('loanDetailsModal').style.display = 'none';
        }

        // وظائف مساعدة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-DZ', {
                style: 'decimal',
                minimumFractionDigits: 2
            }).format(amount);
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-DZ');
        }

        function getStatusClass(status) {
            switch (status) {
                case 'موافق عليه': return 'approved';
                case 'مرفوض': return 'rejected';
                case 'مكتمل': return 'approved';
                default: return 'pending';
            }
        }

        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alert.style.position = 'fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.style.minWidth = '300px';
            
            document.body.appendChild(alert);
            
            setTimeout(() => {
                document.body.removeChild(alert);
            }, 5000);
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                window.location.href = 'auth.php?action=logout';
            }
        }
    </script>

    <style>
        .loan-details .detail-row {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
        }
        
        .loan-details .detail-row:last-child {
            border-bottom: none;
        }
    </style>
</body>
</html>
