<?php
require_once 'auth.php';

// التحقق من تسجيل الدخول
$auth->requireLogin();

header('Content-Type: application/json; charset=utf-8');

class RevenueManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    public function getAllRevenues() {
        try {
            $stmt = $this->db->query("
                SELECT r.*, u.full_name as created_by_name
                FROM revenues r
                LEFT JOIN users u ON r.created_by = u.id
                ORDER BY r.date DESC, r.created_at DESC
            ");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع بيانات الإيرادات: " . $e->getMessage());
        }
    }
    
    public function getRevenueById($id) {
        try {
            $stmt = $this->db->prepare("
                SELECT r.*, u.full_name as created_by_name
                FROM revenues r
                LEFT JOIN users u ON r.created_by = u.id
                WHERE r.id = ?
            ");
            $stmt->execute([$id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع بيانات الإيراد: " . $e->getMessage());
        }
    }
    
    public function createRevenue($data, $userId) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO revenues (source_type, amount, description, date, created_by)
                VALUES (?, ?, ?, ?, ?)
            ");
            
            $result = $stmt->execute([
                $data['source_type'],
                $data['amount'],
                $data['description'] ?? '',
                $data['date'],
                $userId
            ]);
            
            if ($result) {
                return $this->db->lastInsertId();
            } else {
                throw new Exception("فشل في إضافة الإيراد");
            }
        } catch (PDOException $e) {
            throw new Exception("خطأ في إضافة الإيراد: " . $e->getMessage());
        }
    }
    
    public function updateRevenue($id, $data) {
        try {
            $stmt = $this->db->prepare("
                UPDATE revenues SET 
                    source_type = ?, amount = ?, description = ?, date = ?
                WHERE id = ?
            ");
            
            $result = $stmt->execute([
                $data['source_type'],
                $data['amount'],
                $data['description'] ?? '',
                $data['date'],
                $id
            ]);
            
            if (!$result) {
                throw new Exception("فشل في تحديث الإيراد");
            }
            
            return true;
        } catch (PDOException $e) {
            throw new Exception("خطأ في تحديث الإيراد: " . $e->getMessage());
        }
    }
    
    public function deleteRevenue($id) {
        try {
            $stmt = $this->db->prepare("DELETE FROM revenues WHERE id = ?");
            $result = $stmt->execute([$id]);
            
            if (!$result) {
                throw new Exception("فشل في حذف الإيراد");
            }
            
            return true;
        } catch (PDOException $e) {
            throw new Exception("خطأ في حذف الإيراد: " . $e->getMessage());
        }
    }
    
    public function getRevenueStatistics() {
        try {
            $stats = [];
            
            // إجمالي الإيرادات
            $stmt = $this->db->query("SELECT COALESCE(SUM(amount), 0) as total FROM revenues");
            $stats['total'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // الإعانات الحكومية
            $stmt = $this->db->query("
                SELECT COALESCE(SUM(amount), 0) as government_support 
                FROM revenues 
                WHERE source_type = 'إعانة الدولة'
            ");
            $stats['government_support'] = $stmt->fetch(PDO::FETCH_ASSOC)['government_support'];
            
            // التبرعات
            $stmt = $this->db->query("
                SELECT COALESCE(SUM(amount), 0) as donations 
                FROM revenues 
                WHERE source_type = 'تبرعات'
            ");
            $stats['donations'] = $stmt->fetch(PDO::FETCH_ASSOC)['donations'];
            
            // الفوائد البنكية
            $stmt = $this->db->query("
                SELECT COALESCE(SUM(amount), 0) as bank_interest 
                FROM revenues 
                WHERE source_type = 'فوائد بنكية'
            ");
            $stats['bank_interest'] = $stmt->fetch(PDO::FETCH_ASSOC)['bank_interest'];
            
            // الإيرادات الشهرية (آخر 12 شهر)
            $monthlyRevenues = [];
            for ($i = 11; $i >= 0; $i--) {
                $month = date('Y-m', strtotime("-$i months"));
                $stmt = $this->db->prepare("
                    SELECT COALESCE(SUM(amount), 0) as total 
                    FROM revenues 
                    WHERE strftime('%Y-%m', date) = ?
                ");
                $stmt->execute([$month]);
                $monthlyRevenues[] = [
                    'month' => $month,
                    'total' => floatval($stmt->fetch(PDO::FETCH_ASSOC)['total'])
                ];
            }
            $stats['monthly_revenues'] = $monthlyRevenues;
            
            // توزيع الإيرادات حسب المصدر
            $stmt = $this->db->query("
                SELECT source_type, COALESCE(SUM(amount), 0) as total, COUNT(*) as count
                FROM revenues 
                GROUP BY source_type
                ORDER BY total DESC
            ");
            $stats['by_source'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return $stats;
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع الإحصائيات: " . $e->getMessage());
        }
    }
    
    public function getRevenuesByDateRange($startDate, $endDate) {
        try {
            $stmt = $this->db->prepare("
                SELECT r.*, u.full_name as created_by_name
                FROM revenues r
                LEFT JOIN users u ON r.created_by = u.id
                WHERE r.date BETWEEN ? AND ?
                ORDER BY r.date DESC
            ");
            $stmt->execute([$startDate, $endDate]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع الإيرادات: " . $e->getMessage());
        }
    }
    
    public function getRevenuesBySource($sourceType) {
        try {
            $stmt = $this->db->prepare("
                SELECT r.*, u.full_name as created_by_name
                FROM revenues r
                LEFT JOIN users u ON r.created_by = u.id
                WHERE r.source_type = ?
                ORDER BY r.date DESC
            ");
            $stmt->execute([$sourceType]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع الإيرادات: " . $e->getMessage());
        }
    }
    
    public function getMonthlyReport($year, $month) {
        try {
            $startDate = "$year-$month-01";
            $endDate = date('Y-m-t', strtotime($startDate));
            
            $report = [];
            
            // إجمالي الإيرادات للشهر
            $stmt = $this->db->prepare("
                SELECT COALESCE(SUM(amount), 0) as total, COUNT(*) as count
                FROM revenues 
                WHERE date BETWEEN ? AND ?
            ");
            $stmt->execute([$startDate, $endDate]);
            $report['summary'] = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // التوزيع حسب المصدر
            $stmt = $this->db->prepare("
                SELECT source_type, COALESCE(SUM(amount), 0) as total, COUNT(*) as count
                FROM revenues 
                WHERE date BETWEEN ? AND ?
                GROUP BY source_type
                ORDER BY total DESC
            ");
            $stmt->execute([$startDate, $endDate]);
            $report['by_source'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // الإيرادات اليومية
            $stmt = $this->db->prepare("
                SELECT date, COALESCE(SUM(amount), 0) as total, COUNT(*) as count
                FROM revenues 
                WHERE date BETWEEN ? AND ?
                GROUP BY date
                ORDER BY date ASC
            ");
            $stmt->execute([$startDate, $endDate]);
            $report['daily'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return $report;
        } catch (PDOException $e) {
            throw new Exception("خطأ في إنشاء التقرير: " . $e->getMessage());
        }
    }
}

// معالجة الطلبات
try {
    $revenueManager = new RevenueManager($pdo);
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    $currentUser = $auth->getCurrentUser();
    
    switch ($action) {
        case 'list':
            $revenues = $revenueManager->getAllRevenues();
            echo json_encode([
                'success' => true,
                'data' => $revenues
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'get':
            $id = $_GET['id'] ?? 0;
            if (!$id) {
                throw new Exception("معرف الإيراد مطلوب");
            }
            
            $revenue = $revenueManager->getRevenueById($id);
            if (!$revenue) {
                throw new Exception("الإيراد غير موجود");
            }
            
            echo json_encode([
                'success' => true,
                'data' => $revenue
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'create':
            $requiredFields = ['source_type', 'amount', 'date'];
            
            foreach ($requiredFields as $field) {
                if (empty($_POST[$field])) {
                    throw new Exception("الحقل $field مطلوب");
                }
            }
            
            // التحقق من صحة المبلغ
            if (!is_numeric($_POST['amount']) || $_POST['amount'] <= 0) {
                throw new Exception("المبلغ يجب أن يكون رقماً موجباً");
            }
            
            // التحقق من صحة التاريخ
            if (!strtotime($_POST['date'])) {
                throw new Exception("تاريخ غير صحيح");
            }
            
            $revenueId = $revenueManager->createRevenue($_POST, $currentUser['id']);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم إضافة الإيراد بنجاح',
                'revenue_id' => $revenueId
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'update':
            $id = $_POST['id'] ?? 0;
            if (!$id) {
                throw new Exception("معرف الإيراد مطلوب");
            }
            
            $requiredFields = ['source_type', 'amount', 'date'];
            
            foreach ($requiredFields as $field) {
                if (empty($_POST[$field])) {
                    throw new Exception("الحقل $field مطلوب");
                }
            }
            
            // التحقق من صحة المبلغ
            if (!is_numeric($_POST['amount']) || $_POST['amount'] <= 0) {
                throw new Exception("المبلغ يجب أن يكون رقماً موجباً");
            }
            
            $revenueManager->updateRevenue($id, $_POST);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث الإيراد بنجاح'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'delete':
            $id = $_POST['id'] ?? 0;
            if (!$id) {
                throw new Exception("معرف الإيراد مطلوب");
            }
            
            $revenueManager->deleteRevenue($id);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم حذف الإيراد بنجاح'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'statistics':
            $stats = $revenueManager->getRevenueStatistics();
            
            echo json_encode([
                'success' => true,
                'data' => $stats
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'by_date_range':
            $startDate = $_GET['start_date'] ?? '';
            $endDate = $_GET['end_date'] ?? '';
            
            if (!$startDate || !$endDate) {
                throw new Exception("تاريخ البداية والنهاية مطلوبان");
            }
            
            $revenues = $revenueManager->getRevenuesByDateRange($startDate, $endDate);
            
            echo json_encode([
                'success' => true,
                'data' => $revenues
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'by_source':
            $sourceType = $_GET['source_type'] ?? '';
            
            if (!$sourceType) {
                throw new Exception("نوع المصدر مطلوب");
            }
            
            $revenues = $revenueManager->getRevenuesBySource($sourceType);
            
            echo json_encode([
                'success' => true,
                'data' => $revenues
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'monthly_report':
            $year = $_GET['year'] ?? date('Y');
            $month = $_GET['month'] ?? date('m');
            
            $report = $revenueManager->getMonthlyReport($year, $month);
            
            echo json_encode([
                'success' => true,
                'data' => $report
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            throw new Exception("إجراء غير صحيح");
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
