<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الاصطياف - لجنة الخدمات الاجتماعية</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="dashboard">
    <!-- شريط التنقل العلوي -->
    <nav class="navbar">
        <div class="navbar-content">
            <a href="dashboard.html" class="navbar-brand">
                <i class="fas fa-users"></i>
                لجنة الخدمات الاجتماعية - ولاية الجلفة
            </a>
            <div class="navbar-user">
                <div class="user-info">
                    <i class="fas fa-user"></i>
                    <span id="userName">مرحباً، مدير النظام</span>
                </div>
                <button class="btn btn-logout" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>
    </nav>

    <!-- الشريط الجانبي -->
    <aside class="sidebar">
        <ul class="sidebar-menu">
            <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i>لوحة التحكم</a></li>
            <li><a href="employees.html"><i class="fas fa-users"></i>إدارة الموظفين</a></li>
            <li><a href="revenues.html"><i class="fas fa-arrow-up"></i>الإيرادات</a></li>
            <li><a href="expenses.html"><i class="fas fa-arrow-down"></i>المصروفات</a></li>
            <li><a href="loans.html"><i class="fas fa-money-bill-wave"></i>السلفات المالية</a></li>
            <li><a href="umrah.html"><i class="fas fa-kaaba"></i>العمرة</a></li>
            <li><a href="vacation.html" class="active"><i class="fas fa-umbrella-beach"></i>الاصطياف</a></li>
            <li><a href="reports.html"><i class="fas fa-chart-bar"></i>التقارير والإحصائيات</a></li>
            <li><a href="settings.html"><i class="fas fa-cog"></i>الإعدادات</a></li>
        </ul>
    </aside>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">إدارة الاصطياف</h1>
            <p class="page-subtitle">إدارة الرحلات الصيفية والاصطياف للموظفين وعائلاتهم</p>
        </div>

        <!-- إحصائيات الاصطياف -->
        <div class="stats-grid" style="margin-bottom: 30px;">
            <div class="stat-card">
                <div class="stat-icon blue">
                    <i class="fas fa-umbrella-beach"></i>
                </div>
                <div class="stat-number" id="totalVacations">0</div>
                <div class="stat-label">إجمالي الطلبات</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon green">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number" id="totalParticipants">0</div>
                <div class="stat-label">إجمالي المشاركين</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon orange">
                    <i class="fas fa-child"></i>
                </div>
                <div class="stat-number" id="totalChildren">0</div>
                <div class="stat-label">الأطفال المشاركين</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon purple">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-number currency" id="totalVacationCost">0</div>
                <div class="stat-label">إجمالي التكلفة</div>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div style="margin-bottom: 20px;">
            <button class="btn btn-primary" onclick="showAddVacationModal()">
                <i class="fas fa-plus"></i>
                طلب اصطياف جديد
            </button>
            <button class="btn btn-success" onclick="exportVacationData()">
                <i class="fas fa-download"></i>
                تصدير البيانات
            </button>
            <button class="btn btn-info" onclick="showDestinationsModal()">
                <i class="fas fa-map-marker-alt"></i>
                إدارة الوجهات
            </button>
        </div>

        <!-- البحث والفلترة -->
        <div class="card" style="margin-bottom: 20px;">
            <div class="card-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="searchVacation">البحث</label>
                        <input type="text" id="searchVacation" class="form-control" placeholder="البحث بالاسم أو رقم التسجيل">
                    </div>
                    <div class="form-group">
                        <label for="filterDestination">الوجهة</label>
                        <select id="filterDestination" class="form-control">
                            <option value="">جميع الوجهات</option>
                            <option value="بجاية">بجاية</option>
                            <option value="سكيكدة">سكيكدة</option>
                            <option value="عنابة">عنابة</option>
                            <option value="تلمسان">تلمسان</option>
                            <option value="وهران">وهران</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="filterStatus">الحالة</label>
                        <select id="filterStatus" class="form-control">
                            <option value="">جميع الحالات</option>
                            <option value="قيد الدراسة">قيد الدراسة</option>
                            <option value="مقبول">مقبول</option>
                            <option value="مرفوض">مرفوض</option>
                            <option value="مكتمل">مكتمل</option>
                        </select>
                    </div>
                    <div class="form-group" style="display: flex; align-items: end;">
                        <button class="btn btn-info" onclick="filterVacations()">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول طلبات الاصطياف -->
        <div class="table-container">
            <div class="table-header">
                <h3 class="table-title">قائمة طلبات الاصطياف</h3>
                <button class="btn btn-primary btn-sm" onclick="loadVacations()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث
                </button>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>الموظف</th>
                        <th>عدد أفراد الأسرة</th>
                        <th>الأطفال المشاركين</th>
                        <th>الوجهة</th>
                        <th>المدة</th>
                        <th>تاريخ السفر</th>
                        <th>التكلفة الإجمالية</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="vacationsTable">
                    <tr>
                        <td colspan="9" style="text-align: center; padding: 40px;">
                            <div class="loading">
                                <div class="spinner"></div>
                                <p>جاري تحميل البيانات...</p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </main>

    <!-- نافذة إضافة/تعديل طلب اصطياف -->
    <div id="vacationModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="vacationModalTitle">طلب اصطياف جديد</h3>
                <button class="btn-close" onclick="closeVacationModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="vacationForm">
                    <input type="hidden" id="vacationId" name="id">
                    
                    <div class="form-group">
                        <label for="vacationEmployee">الموظف *</label>
                        <select id="vacationEmployee" name="employee_id" class="form-control" required>
                            <option value="">اختر الموظف</option>
                        </select>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="familyMembersCount">عدد أفراد الأسرة *</label>
                            <input type="number" id="familyMembersCount" name="family_members_count" class="form-control" min="1" required>
                        </div>
                        <div class="form-group">
                            <label for="childrenParticipating">الأطفال المشاركين</label>
                            <input type="number" id="childrenParticipating" name="children_participating" class="form-control" min="0" value="0">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="vacationDestination">الوجهة المطلوبة</label>
                            <select id="vacationDestination" name="destination" class="form-control">
                                <option value="">اختر الوجهة</option>
                                <option value="بجاية">بجاية</option>
                                <option value="سكيكدة">سكيكدة</option>
                                <option value="عنابة">عنابة</option>
                                <option value="تلمسان">تلمسان</option>
                                <option value="وهران">وهران</option>
                                <option value="الطارف">الطارف</option>
                                <option value="جيجل">جيجل</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="durationDays">المدة (أيام)</label>
                            <input type="number" id="durationDays" name="duration_days" class="form-control" min="1" max="15">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="travelDate">تاريخ السفر المقترح</label>
                        <input type="date" id="travelDate" name="travel_date" class="form-control">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="vacationTotalCost">التكلفة الإجمالية (د.ج)</label>
                            <input type="number" id="vacationTotalCost" name="total_cost" class="form-control" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="vacationCommitteeContribution">مساهمة اللجنة (د.ج)</label>
                            <input type="number" id="vacationCommitteeContribution" name="committee_contribution" class="form-control" step="0.01">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="vacationEmployeeContribution">مساهمة الموظف (د.ج)</label>
                        <input type="number" id="vacationEmployeeContribution" name="employee_contribution" class="form-control" step="0.01" readonly>
                    </div>
                    
                    <div class="form-group">
                        <label for="vacationAttachments">المرفقات</label>
                        <input type="file" id="vacationAttachments" name="attachments" class="form-control" multiple accept=".pdf,.jpg,.jpeg,.png">
                        <small class="form-text">شهادة عائلية، بطاقة الخدمة، وثائق أخرى</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeVacationModal()">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveVacation()">حفظ الطلب</button>
            </div>
        </div>
    </div>

    <script>
        let vacations = [];
        let employees = [];

        // تحميل البيانات عند تحميل الصفحة
        window.addEventListener('load', function() {
            loadEmployees();
            loadVacations();
            loadStatistics();
        });

        // تحميل قائمة الموظفين
        async function loadEmployees() {
            try {
                const response = await fetch('employees.php?action=list');
                const result = await response.json();
                
                if (result.success) {
                    employees = result.data;
                    populateEmployeeSelect();
                }
            } catch (error) {
                console.error('خطأ في تحميل الموظفين:', error);
            }
        }

        // ملء قائمة الموظفين
        function populateEmployeeSelect() {
            const select = document.getElementById('vacationEmployee');
            select.innerHTML = '<option value="">اختر الموظف</option>';
            
            employees.forEach(employee => {
                const option = document.createElement('option');
                option.value = employee.id;
                option.textContent = `${employee.full_name} - ${employee.registration_number}`;
                select.appendChild(option);
            });
        }

        // حساب مساهمة الموظف تلقائياً
        document.getElementById('vacationTotalCost').addEventListener('input', calculateEmployeeContribution);
        document.getElementById('vacationCommitteeContribution').addEventListener('input', calculateEmployeeContribution);

        function calculateEmployeeContribution() {
            const totalCost = parseFloat(document.getElementById('vacationTotalCost').value) || 0;
            const committeeContribution = parseFloat(document.getElementById('vacationCommitteeContribution').value) || 0;
            
            const employeeContribution = Math.max(0, totalCost - committeeContribution);
            document.getElementById('vacationEmployeeContribution').value = employeeContribution.toFixed(2);
        }

        // تحميل طلبات الاصطياف
        async function loadVacations() {
            try {
                const response = await fetch('vacation.php?action=list');
                const result = await response.json();
                
                if (result.success) {
                    vacations = result.data;
                    displayVacations(vacations);
                } else {
                    showAlert('خطأ في تحميل البيانات: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('خطأ في تحميل طلبات الاصطياف:', error);
                showAlert('حدث خطأ في تحميل البيانات', 'danger');
            }
        }

        // تحميل الإحصائيات
        async function loadStatistics() {
            try {
                const response = await fetch('vacation.php?action=statistics');
                const result = await response.json();
                
                if (result.success) {
                    const stats = result.data;
                    document.getElementById('totalVacations').textContent = stats.total || 0;
                    document.getElementById('totalParticipants').textContent = stats.total_participants || 0;
                    document.getElementById('totalChildren').textContent = stats.total_children || 0;
                    document.getElementById('totalVacationCost').textContent = formatCurrency(stats.total_cost || 0);
                }
            } catch (error) {
                console.error('خطأ في تحميل الإحصائيات:', error);
            }
        }

        // عرض طلبات الاصطياف في الجدول
        function displayVacations(vacationsList) {
            const tableBody = document.getElementById('vacationsTable');
            
            if (vacationsList.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="9" style="text-align: center; padding: 40px;">لا توجد طلبات اصطياف</td></tr>';
                return;
            }
            
            tableBody.innerHTML = vacationsList.map(vacation => `
                <tr>
                    <td>${vacation.employee_name || 'غير محدد'}</td>
                    <td>${vacation.family_members_count}</td>
                    <td>${vacation.children_participating}</td>
                    <td>${vacation.destination || '-'}</td>
                    <td>${vacation.duration_days ? vacation.duration_days + ' أيام' : '-'}</td>
                    <td>${formatDate(vacation.travel_date)}</td>
                    <td class="currency">${formatCurrency(vacation.total_cost || 0)}</td>
                    <td><span class="status status-${getStatusClass(vacation.status)}">${vacation.status}</span></td>
                    <td>
                        <button class="btn btn-info btn-sm" onclick="viewVacationDetails(${vacation.id})" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-warning btn-sm" onclick="editVacation(${vacation.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-success btn-sm" onclick="approveVacation(${vacation.id})" title="موافقة">
                            <i class="fas fa-check"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // فلترة طلبات الاصطياف
        function filterVacations() {
            const searchTerm = document.getElementById('searchVacation').value.toLowerCase();
            const destinationFilter = document.getElementById('filterDestination').value;
            const statusFilter = document.getElementById('filterStatus').value;
            
            const filteredVacations = vacations.filter(vacation => {
                const matchesSearch = (vacation.employee_name || '').toLowerCase().includes(searchTerm) || 
                                    (vacation.registration_number || '').toLowerCase().includes(searchTerm);
                const matchesDestination = !destinationFilter || vacation.destination === destinationFilter;
                const matchesStatus = !statusFilter || vacation.status === statusFilter;
                
                return matchesSearch && matchesDestination && matchesStatus;
            });
            
            displayVacations(filteredVacations);
        }

        // إضافة مستمعين للبحث المباشر
        document.getElementById('searchVacation').addEventListener('input', filterVacations);
        document.getElementById('filterDestination').addEventListener('change', filterVacations);
        document.getElementById('filterStatus').addEventListener('change', filterVacations);

        // عرض نافذة إضافة طلب اصطياف
        function showAddVacationModal() {
            document.getElementById('vacationModalTitle').textContent = 'طلب اصطياف جديد';
            document.getElementById('vacationForm').reset();
            document.getElementById('vacationId').value = '';
            document.getElementById('vacationModal').style.display = 'flex';
        }

        // حفظ طلب الاصطياف
        async function saveVacation() {
            const form = document.getElementById('vacationForm');
            const formData = new FormData(form);
            
            const action = document.getElementById('vacationId').value ? 'update' : 'create';
            formData.append('action', action);
            
            try {
                const response = await fetch('vacation.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert(result.message, 'success');
                    closeVacationModal();
                    loadVacations();
                    loadStatistics();
                } else {
                    showAlert('خطأ: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('خطأ في حفظ طلب الاصطياف:', error);
                showAlert('حدث خطأ في حفظ البيانات', 'danger');
            }
        }

        // إغلاق النافذة المنبثقة
        function closeVacationModal() {
            document.getElementById('vacationModal').style.display = 'none';
        }

        // وظائف مساعدة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-DZ', {
                style: 'decimal',
                minimumFractionDigits: 2
            }).format(amount);
        }

        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-DZ');
        }

        function getStatusClass(status) {
            switch (status) {
                case 'مقبول': 
                case 'مكتمل': return 'approved';
                case 'مرفوض': return 'rejected';
                default: return 'pending';
            }
        }

        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alert.style.position = 'fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.style.minWidth = '300px';
            
            document.body.appendChild(alert);
            
            setTimeout(() => {
                document.body.removeChild(alert);
            }, 5000);
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                window.location.href = 'index.html';
            }
        }
    </script>
</body>
</html>
