<?php
require_once 'auth.php';

// التحقق من تسجيل الدخول
$auth->requireLogin();

header('Content-Type: application/json; charset=utf-8');

class UmrahManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    public function getAllUmrahApplications() {
        try {
            $stmt = $this->db->query("
                SELECT u.*, e.full_name as employee_name, e.registration_number, 
                       e.rank_position, e.hire_date, usr.full_name as created_by_name
                FROM umrah u
                LEFT JOIN employees e ON u.employee_id = e.id
                LEFT JOIN users usr ON u.created_by = usr.id
                ORDER BY u.created_at DESC
            ");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع بيانات طلبات العمرة: " . $e->getMessage());
        }
    }
    
    public function getUmrahApplicationById($id) {
        try {
            $stmt = $this->db->prepare("
                SELECT u.*, e.full_name as employee_name, e.registration_number, 
                       e.rank_position, e.hire_date, usr.full_name as created_by_name
                FROM umrah u
                LEFT JOIN employees e ON u.employee_id = e.id
                LEFT JOIN users usr ON u.created_by = usr.id
                WHERE u.id = ?
            ");
            $stmt->execute([$id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع بيانات طلب العمرة: " . $e->getMessage());
        }
    }
    
    public function createUmrahApplication($data, $userId) {
        try {
            // التحقق من عدم وجود طلب نشط للموظف
            $stmt = $this->db->prepare("
                SELECT COUNT(*) FROM umrah 
                WHERE employee_id = ? AND status IN ('قيد الدراسة', 'مقبول')
            ");
            $stmt->execute([$data['employee_id']]);
            
            if ($stmt->fetchColumn() > 0) {
                throw new Exception("يوجد طلب عمرة نشط للموظف");
            }
            
            $stmt = $this->db->prepare("
                INSERT INTO umrah (
                    employee_id, previous_umrah_count, has_companion, companion_name,
                    total_cost, committee_contribution, employee_contribution,
                    travel_agency, attachments, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $result = $stmt->execute([
                $data['employee_id'],
                $data['previous_umrah_count'] ?? 0,
                $data['has_companion'] ?? 0,
                $data['companion_name'] ?? '',
                $data['total_cost'] ?? 0,
                $data['committee_contribution'] ?? 0,
                $data['employee_contribution'] ?? 0,
                $data['travel_agency'] ?? '',
                $data['attachments'] ?? '',
                $userId
            ]);
            
            if ($result) {
                return $this->db->lastInsertId();
            } else {
                throw new Exception("فشل في إضافة طلب العمرة");
            }
        } catch (PDOException $e) {
            throw new Exception("خطأ في إضافة طلب العمرة: " . $e->getMessage());
        }
    }
    
    public function updateUmrahApplication($id, $data) {
        try {
            $stmt = $this->db->prepare("
                UPDATE umrah SET 
                    employee_id = ?, previous_umrah_count = ?, has_companion = ?, 
                    companion_name = ?, total_cost = ?, committee_contribution = ?, 
                    employee_contribution = ?, travel_agency = ?, attachments = ?
                WHERE id = ?
            ");
            
            $result = $stmt->execute([
                $data['employee_id'],
                $data['previous_umrah_count'] ?? 0,
                $data['has_companion'] ?? 0,
                $data['companion_name'] ?? '',
                $data['total_cost'] ?? 0,
                $data['committee_contribution'] ?? 0,
                $data['employee_contribution'] ?? 0,
                $data['travel_agency'] ?? '',
                $data['attachments'] ?? '',
                $id
            ]);
            
            if (!$result) {
                throw new Exception("فشل في تحديث طلب العمرة");
            }
            
            return true;
        } catch (PDOException $e) {
            throw new Exception("خطأ في تحديث طلب العمرة: " . $e->getMessage());
        }
    }
    
    public function updateUmrahStatus($id, $status) {
        try {
            $stmt = $this->db->prepare("UPDATE umrah SET status = ? WHERE id = ?");
            $result = $stmt->execute([$status, $id]);
            
            if (!$result) {
                throw new Exception("فشل في تحديث حالة طلب العمرة");
            }
            
            return true;
        } catch (PDOException $e) {
            throw new Exception("خطأ في تحديث حالة طلب العمرة: " . $e->getMessage());
        }
    }
    
    public function deleteUmrahApplication($id) {
        try {
            $stmt = $this->db->prepare("DELETE FROM umrah WHERE id = ?");
            $result = $stmt->execute([$id]);
            
            if (!$result) {
                throw new Exception("فشل في حذف طلب العمرة");
            }
            
            return true;
        } catch (PDOException $e) {
            throw new Exception("خطأ في حذف طلب العمرة: " . $e->getMessage());
        }
    }
    
    public function getUmrahStatistics() {
        try {
            $stats = [];
            
            // إجمالي الطلبات
            $stmt = $this->db->query("SELECT COUNT(*) as total FROM umrah");
            $stats['total'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // الطلبات المقبولة
            $stmt = $this->db->query("SELECT COUNT(*) as approved FROM umrah WHERE status = 'مقبول'");
            $stats['approved'] = $stmt->fetch(PDO::FETCH_ASSOC)['approved'];
            
            // الطلبات قيد الدراسة
            $stmt = $this->db->query("SELECT COUNT(*) as pending FROM umrah WHERE status = 'قيد الدراسة'");
            $stats['pending'] = $stmt->fetch(PDO::FETCH_ASSOC)['pending'];
            
            // إجمالي التكلفة
            $stmt = $this->db->query("SELECT COALESCE(SUM(total_cost), 0) as total_cost FROM umrah WHERE status = 'مقبول'");
            $stats['total_cost'] = $stmt->fetch(PDO::FETCH_ASSOC)['total_cost'];
            
            // توزيع حسب عدد مرات العمرة السابقة
            $stmt = $this->db->query("
                SELECT previous_umrah_count, COUNT(*) as count
                FROM umrah 
                GROUP BY previous_umrah_count
                ORDER BY previous_umrah_count ASC
            ");
            $stats['by_previous_count'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // توزيع حسب الحالة
            $stmt = $this->db->query("
                SELECT status, COUNT(*) as count
                FROM umrah 
                GROUP BY status
            ");
            $stats['by_status'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return $stats;
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع الإحصائيات: " . $e->getMessage());
        }
    }
    
    public function getEligibleEmployees() {
        try {
            // الموظفون المؤهلون (لم يحصلوا على عمرة في السنوات الأخيرة)
            $stmt = $this->db->query("
                SELECT e.*, 
                       COALESCE(u.previous_umrah_count, 0) as last_umrah_count,
                       COALESCE(MAX(u.created_at), '1900-01-01') as last_umrah_date
                FROM employees e
                LEFT JOIN umrah u ON e.id = u.employee_id AND u.status = 'مكتمل'
                WHERE e.id NOT IN (
                    SELECT employee_id FROM umrah 
                    WHERE status IN ('قيد الدراسة', 'مقبول') 
                    AND employee_id IS NOT NULL
                )
                GROUP BY e.id
                ORDER BY last_umrah_date ASC, e.hire_date ASC
            ");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع الموظفين المؤهلين: " . $e->getMessage());
        }
    }
    
    public function conductLottery($maxSelections = 10) {
        try {
            // الحصول على الطلبات المعلقة
            $stmt = $this->db->query("
                SELECT u.*, e.full_name as employee_name, e.hire_date
                FROM umrah u
                JOIN employees e ON u.employee_id = e.id
                WHERE u.status = 'قيد الدراسة'
                ORDER BY u.previous_umrah_count ASC, e.hire_date ASC
            ");
            $pendingApplications = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (empty($pendingApplications)) {
                throw new Exception("لا توجد طلبات معلقة للقرعة");
            }
            
            // تطبيق معايير الأولوية
            $priorityGroups = [
                'first_time' => [], // لم يحصل على عمرة من قبل
                'second_time' => [], // حصل على عمرة مرة واحدة
                'multiple_times' => [] // حصل على عمرة أكثر من مرة
            ];
            
            foreach ($pendingApplications as $app) {
                if ($app['previous_umrah_count'] == 0) {
                    $priorityGroups['first_time'][] = $app;
                } elseif ($app['previous_umrah_count'] == 1) {
                    $priorityGroups['second_time'][] = $app;
                } else {
                    $priorityGroups['multiple_times'][] = $app;
                }
            }
            
            $selectedApplications = [];
            $remainingSlots = $maxSelections;
            
            // اختيار من المجموعة الأولى أولاً
            foreach ($priorityGroups as $group) {
                if ($remainingSlots <= 0) break;
                
                $groupSelections = min($remainingSlots, count($group));
                if ($groupSelections > 0) {
                    shuffle($group); // خلط عشوائي
                    $selected = array_slice($group, 0, $groupSelections);
                    $selectedApplications = array_merge($selectedApplications, $selected);
                    $remainingSlots -= $groupSelections;
                }
            }
            
            return [
                'selected' => $selectedApplications,
                'total_pending' => count($pendingApplications),
                'selected_count' => count($selectedApplications)
            ];
            
        } catch (PDOException $e) {
            throw new Exception("خطأ في إجراء القرعة: " . $e->getMessage());
        }
    }
    
    public function approveSelectedApplications($selectedIds) {
        try {
            $this->db->beginTransaction();
            
            $stmt = $this->db->prepare("UPDATE umrah SET status = 'مقبول' WHERE id = ?");
            
            foreach ($selectedIds as $id) {
                $stmt->execute([$id]);
            }
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }
}

// معالجة الطلبات
try {
    $umrahManager = new UmrahManager($pdo);
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    $currentUser = $auth->getCurrentUser();
    
    switch ($action) {
        case 'list':
            $applications = $umrahManager->getAllUmrahApplications();
            echo json_encode([
                'success' => true,
                'data' => $applications
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'get':
            $id = $_GET['id'] ?? 0;
            if (!$id) {
                throw new Exception("معرف طلب العمرة مطلوب");
            }
            
            $application = $umrahManager->getUmrahApplicationById($id);
            if (!$application) {
                throw new Exception("طلب العمرة غير موجود");
            }
            
            echo json_encode([
                'success' => true,
                'data' => $application
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'create':
            $requiredFields = ['employee_id'];
            
            foreach ($requiredFields as $field) {
                if (empty($_POST[$field])) {
                    throw new Exception("الحقل $field مطلوب");
                }
            }
            
            $applicationId = $umrahManager->createUmrahApplication($_POST, $currentUser['id']);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم إضافة طلب العمرة بنجاح',
                'application_id' => $applicationId
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'update':
            $id = $_POST['id'] ?? 0;
            if (!$id) {
                throw new Exception("معرف طلب العمرة مطلوب");
            }
            
            $umrahManager->updateUmrahApplication($id, $_POST);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث طلب العمرة بنجاح'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'update_status':
            $id = $_POST['id'] ?? 0;
            $status = $_POST['status'] ?? '';
            
            if (!$id || !$status) {
                throw new Exception("معرف طلب العمرة والحالة مطلوبان");
            }
            
            $umrahManager->updateUmrahStatus($id, $status);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث حالة طلب العمرة بنجاح'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'delete':
            $id = $_POST['id'] ?? 0;
            if (!$id) {
                throw new Exception("معرف طلب العمرة مطلوب");
            }
            
            $umrahManager->deleteUmrahApplication($id);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم حذف طلب العمرة بنجاح'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'statistics':
            $stats = $umrahManager->getUmrahStatistics();
            
            echo json_encode([
                'success' => true,
                'data' => $stats
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'eligible_employees':
            $employees = $umrahManager->getEligibleEmployees();
            
            echo json_encode([
                'success' => true,
                'data' => $employees
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'conduct_lottery':
            $maxSelections = $_POST['max_selections'] ?? 10;
            $lotteryResult = $umrahManager->conductLottery($maxSelections);
            
            echo json_encode([
                'success' => true,
                'data' => $lotteryResult
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'approve_selected':
            $selectedIds = $_POST['selected_ids'] ?? [];
            if (empty($selectedIds)) {
                throw new Exception("لم يتم تحديد أي طلبات");
            }
            
            $umrahManager->approveSelectedApplications($selectedIds);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم اعتماد الطلبات المحددة بنجاح'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            throw new Exception("إجراء غير صحيح");
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
