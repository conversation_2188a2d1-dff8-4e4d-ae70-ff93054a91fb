# دليل التثبيت والتشغيل - نظام إدارة لجنة الخدمات الاجتماعية

## متطلبات النظام

### الحد الأدنى من المتطلبات:
- **نظام التشغيل**: Windows 7/10/11, macOS 10.12+, Linux Ubuntu 16.04+
- **خادم الويب**: Apache 2.4+ أو Nginx 1.10+ أو PHP Built-in Server
- **PHP**: الإصدار 7.4 أو أحدث
- **الذاكرة**: 512 MB RAM كحد أدنى
- **مساحة القرص**: 100 MB مساحة فارغة
- **المتصفح**: Chrome 70+, Firefox 65+, Safari 12+, Edge 79+

### المتطلبات الموصى بها:
- **PHP**: الإصدار 8.0 أو أحدث
- **الذاكرة**: 1 GB RAM أو أكثر
- **مساحة القرص**: 500 MB مساحة فارغة

## طرق التثبيت

### الطريقة الأولى: استخدام XAMPP (الأسهل للمبتدئين)

#### 1. تحميل وتثبيت XAMPP
```
1. انتقل إلى https://www.apachefriends.org/
2. حمل XAMPP للنظام الخاص بك
3. ثبت XAMPP باتباع التعليمات
4. شغل XAMPP Control Panel
5. ابدأ تشغيل Apache
```

#### 2. نسخ ملفات المشروع
```
1. انسخ جميع ملفات المشروع
2. الصق الملفات في مجلد: C:\xampp\htdocs\social-services\
3. تأكد من وجود جميع الملفات في المجلد
```

#### 3. الوصول للنظام
```
1. افتح المتصفح
2. انتقل إلى: http://localhost/social-services/
3. ستظهر لك صفحة تسجيل الدخول
```

### الطريقة الثانية: استخدام خادم PHP المدمج

#### 1. التحقق من تثبيت PHP
```bash
# في سطر الأوامر (Command Prompt أو Terminal)
php --version
```

#### 2. تشغيل الخادم
```bash
# انتقل إلى مجلد المشروع
cd path/to/social-services

# شغل الخادم المحلي
php -S localhost:8000
```

#### 3. الوصول للنظام
```
افتح المتصفح وانتقل إلى: http://localhost:8000
```

### الطريقة الثالثة: استخدام WAMP (لنظام Windows)

#### 1. تحميل وتثبيت WAMP
```
1. انتقل إلى http://www.wampserver.com/
2. حمل WampServer
3. ثبت البرنامج
4. شغل WampServer
```

#### 2. نسخ الملفات
```
1. انسخ ملفات المشروع
2. الصق في: C:\wamp64\www\social-services\
3. تأكد من تشغيل جميع الخدمات (أخضر)
```

#### 3. الوصول للنظام
```
انتقل إلى: http://localhost/social-services/
```

## إعداد قاعدة البيانات

### إعداد تلقائي (موصى به)
```
1. عند أول زيارة للنظام، ستُنشأ قاعدة البيانات تلقائياً
2. ستُضاف البيانات التجريبية تلقائياً
3. سيُنشأ المستخدم الافتراضي تلقائياً
```

### التحقق من إنشاء قاعدة البيانات
```
1. تحقق من وجود ملف: social_services.db في مجلد المشروع
2. إذا لم يوجد الملف، تأكد من صلاحيات الكتابة في المجلد
3. تحقق من تفعيل SQLite في PHP
```

## بيانات الدخول الافتراضية

```
اسم المستخدم: admin
كلمة المرور: admin123
```

**مهم**: غيّر كلمة المرور فور تسجيل الدخول الأول!

## التحقق من التثبيت

### 1. اختبار الصفحة الرئيسية
```
- انتقل إلى الصفحة الرئيسية
- يجب أن تظهر صفحة تسجيل الدخول
- تأكد من ظهور النصوص العربية بشكل صحيح
```

### 2. اختبار تسجيل الدخول
```
- أدخل بيانات الدخول الافتراضية
- يجب أن تنتقل إلى لوحة التحكم
- تحقق من ظهور البيانات التجريبية
```

### 3. اختبار الوظائف الأساسية
```
- انتقل إلى "إدارة الموظفين"
- تحقق من ظهور الموظفين التجريبيين
- جرب إضافة موظف جديد
- تحقق من حفظ البيانات
```

## حل المشاكل الشائعة

### مشكلة: لا تظهر الصفحة
```
الحلول:
1. تأكد من تشغيل خادم الويب
2. تحقق من المسار الصحيح
3. تأكد من وجود ملف index.html
4. تحقق من إعدادات الجدار الناري
```

### مشكلة: خطأ في قاعدة البيانات
```
الحلول:
1. تأكد من تفعيل SQLite في PHP
2. تحقق من صلاحيات الكتابة في المجلد
3. احذف ملف social_services.db وأعد تحميل الصفحة
4. تحقق من إصدار PHP (7.4+ مطلوب)
```

### مشكلة: النصوص العربية لا تظهر بشكل صحيح
```
الحلول:
1. تأكد من دعم المتصفح للـ UTF-8
2. تحقق من إعدادات الترميز في المتصفح
3. امسح ذاكرة التخزين المؤقت للمتصفح
4. جرب متصفح آخر
```

### مشكلة: بطء في التحميل
```
الحلول:
1. تأكد من توفر ذاكرة كافية
2. أغلق البرامج غير الضرورية
3. تحقق من سرعة القرص الصلب
4. استخدم SSD إن أمكن
```

## إعدادات متقدمة

### تخصيص إعدادات PHP
```php
// في ملف php.ini
memory_limit = 256M
max_execution_time = 300
upload_max_filesize = 10M
post_max_size = 10M
```

### تفعيل الأخطاء للتطوير
```php
// أضف في بداية ملف index.html
<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
?>
```

### إعداد النسخ الاحتياطي التلقائي
```bash
# إنشاء نسخة احتياطية يومية (Linux/macOS)
0 2 * * * cp /path/to/social_services.db /backup/social_services_$(date +\%Y\%m\%d).db
```

## الأمان والحماية

### تأمين النظام
```
1. غيّر كلمة المرور الافتراضية
2. استخدم HTTPS في البيئة الإنتاجية
3. قم بنسخ احتياطية منتظمة
4. حدّث PHP بانتظام
5. راقب سجلات الوصول
```

### صلاحيات الملفات (Linux/macOS)
```bash
# تعيين الصلاحيات المناسبة
chmod 755 /path/to/social-services/
chmod 644 /path/to/social-services/*.php
chmod 644 /path/to/social-services/*.html
chmod 666 /path/to/social-services/social_services.db
```

## الدعم الفني

### قبل طلب المساعدة
```
1. تحقق من متطلبات النظام
2. راجع قسم حل المشاكل
3. جرب إعادة التثبيت
4. اجمع معلومات النظام (إصدار PHP، نظام التشغيل، المتصفح)
```

### معلومات مفيدة للدعم
```
- إصدار PHP: php --version
- إصدار نظام التشغيل
- نوع وإصدار المتصفح
- رسائل الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة
```

---

**نصيحة**: احتفظ بنسخة احتياطية من قاعدة البيانات قبل أي تحديث أو تعديل!
