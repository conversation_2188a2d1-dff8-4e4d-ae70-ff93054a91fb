<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - لجنة الخدمات الاجتماعية</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="dashboard">
    <!-- شريط التنقل العلوي -->
    <nav class="navbar">
        <div class="navbar-content">
            <a href="dashboard.html" class="navbar-brand">
                <i class="fas fa-users"></i>
                لجنة الخدمات الاجتماعية - ولاية الجلفة
            </a>
            <div class="navbar-user">
                <div class="user-info">
                    <i class="fas fa-user"></i>
                    <span id="userName">مرحباً، مدير النظام</span>
                </div>
                <button class="btn btn-logout" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>
    </nav>

    <!-- الشريط الجانبي -->
    <aside class="sidebar">
        <ul class="sidebar-menu">
            <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i>لوحة التحكم</a></li>
            <li><a href="employees.html"><i class="fas fa-users"></i>إدارة الموظفين</a></li>
            <li><a href="revenues.html"><i class="fas fa-arrow-up"></i>الإيرادات</a></li>
            <li><a href="expenses.html"><i class="fas fa-arrow-down"></i>المصروفات</a></li>
            <li><a href="loans.html"><i class="fas fa-money-bill-wave"></i>السلفات المالية</a></li>
            <li><a href="umrah.html"><i class="fas fa-kaaba"></i>العمرة</a></li>
            <li><a href="vacation.html"><i class="fas fa-umbrella-beach"></i>الاصطياف</a></li>
            <li><a href="reports.html"><i class="fas fa-chart-bar"></i>التقارير والإحصائيات</a></li>
            <li><a href="settings.html" class="active"><i class="fas fa-cog"></i>الإعدادات</a></li>
        </ul>
    </aside>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">إعدادات النظام</h1>
            <p class="page-subtitle">إدارة إعدادات النظام والمستخدمين</p>
        </div>

        <!-- بطاقات الإعدادات -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
            
            <!-- إعدادات المستخدم -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user-cog"></i>
                        إعدادات المستخدم
                    </h3>
                </div>
                <div class="card-body">
                    <form id="userSettingsForm">
                        <div class="form-group">
                            <label for="currentUserName">اسم المستخدم</label>
                            <input type="text" id="currentUserName" class="form-control" value="مدير النظام" readonly>
                        </div>
                        <div class="form-group">
                            <label for="currentPassword">كلمة المرور الحالية</label>
                            <input type="password" id="currentPassword" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="newPassword">كلمة المرور الجديدة</label>
                            <input type="password" id="newPassword" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="confirmPassword">تأكيد كلمة المرور</label>
                            <input type="password" id="confirmPassword" class="form-control">
                        </div>
                        <button type="button" class="btn btn-primary" onclick="changePassword()">
                            <i class="fas fa-save"></i>
                            تغيير كلمة المرور
                        </button>
                    </form>
                </div>
            </div>

            <!-- إعدادات النظام -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-cogs"></i>
                        إعدادات النظام
                    </h3>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="systemName">اسم النظام</label>
                        <input type="text" id="systemName" class="form-control" value="لجنة الخدمات الاجتماعية - ولاية الجلفة">
                    </div>
                    <div class="form-group">
                        <label for="maxLoanAmount">الحد الأقصى للسلفة</label>
                        <input type="number" id="maxLoanAmount" class="form-control" value="500000" step="1000">
                        <small class="form-text">بالدينار الجزائري</small>
                    </div>
                    <div class="form-group">
                        <label for="maxLoanPercentage">النسبة القصوى من الراتب</label>
                        <input type="number" id="maxLoanPercentage" class="form-control" value="50" min="1" max="100">
                        <small class="form-text">النسبة المئوية</small>
                    </div>
                    <button type="button" class="btn btn-success" onclick="saveSystemSettings()">
                        <i class="fas fa-save"></i>
                        حفظ الإعدادات
                    </button>
                </div>
            </div>

            <!-- إدارة المستخدمين -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-users-cog"></i>
                        إدارة المستخدمين
                    </h3>
                </div>
                <div class="card-body">
                    <button class="btn btn-primary" onclick="showAddUserModal()">
                        <i class="fas fa-user-plus"></i>
                        إضافة مستخدم جديد
                    </button>
                    <button class="btn btn-info" onclick="viewActivityLog()">
                        <i class="fas fa-history"></i>
                        سجل الأنشطة
                    </button>
                    <button class="btn btn-warning" onclick="managePermissions()">
                        <i class="fas fa-shield-alt"></i>
                        إدارة الصلاحيات
                    </button>
                </div>
            </div>

            <!-- النسخ الاحتياطي -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-database"></i>
                        النسخ الاحتياطي
                    </h3>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label>آخر نسخة احتياطية</label>
                        <p class="form-control-static" id="lastBackupDate">لم يتم إنشاء نسخة احتياطية بعد</p>
                    </div>
                    <button class="btn btn-success" onclick="createBackup()">
                        <i class="fas fa-download"></i>
                        إنشاء نسخة احتياطية
                    </button>
                    <button class="btn btn-warning" onclick="showRestoreModal()">
                        <i class="fas fa-upload"></i>
                        استعادة النسخة الاحتياطية
                    </button>
                </div>
            </div>

            <!-- إحصائيات النظام -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-pie"></i>
                        إحصائيات النظام
                    </h3>
                </div>
                <div class="card-body">
                    <div class="stat-item">
                        <span>حجم قاعدة البيانات:</span>
                        <span id="databaseSize">حساب...</span>
                    </div>
                    <div class="stat-item">
                        <span>عدد المستخدمين:</span>
                        <span id="usersCount">1</span>
                    </div>
                    <div class="stat-item">
                        <span>عدد الموظفين:</span>
                        <span id="employeesCount">0</span>
                    </div>
                    <div class="stat-item">
                        <span>إجمالي المعاملات:</span>
                        <span id="transactionsCount">0</span>
                    </div>
                </div>
            </div>

            <!-- معلومات النظام -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle"></i>
                        معلومات النظام
                    </h3>
                </div>
                <div class="card-body">
                    <div class="stat-item">
                        <span>إصدار النظام:</span>
                        <span>1.0.0</span>
                    </div>
                    <div class="stat-item">
                        <span>تاريخ التطوير:</span>
                        <span>2024</span>
                    </div>
                    <div class="stat-item">
                        <span>المطور:</span>
                        <span>فريق التطوير</span>
                    </div>
                    <div class="stat-item">
                        <span>الدعم الفني:</span>
                        <span>متاح 24/7</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- سجل الأنشطة -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">سجل الأنشطة الأخيرة</h3>
                <button class="btn btn-primary btn-sm" onclick="loadActivityLog()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث
                </button>
            </div>
            <div class="card-body">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>المستخدم</th>
                                <th>النشاط</th>
                                <th>عنوان IP</th>
                                <th>التاريخ والوقت</th>
                            </tr>
                        </thead>
                        <tbody id="activityLogTable">
                            <tr>
                                <td colspan="4" style="text-align: center; padding: 40px;">
                                    <div class="loading">
                                        <div class="spinner"></div>
                                        <p>جاري تحميل سجل الأنشطة...</p>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- نافذة إضافة مستخدم -->
    <div id="addUserModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إضافة مستخدم جديد</h3>
                <button class="btn-close" onclick="closeAddUserModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="form-group">
                        <label for="newUserName">اسم المستخدم *</label>
                        <input type="text" id="newUserName" name="username" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="newUserFullName">الاسم الكامل *</label>
                        <input type="text" id="newUserFullName" name="full_name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="newUserPassword">كلمة المرور *</label>
                        <input type="password" id="newUserPassword" name="password" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="newUserRole">الدور *</label>
                        <select id="newUserRole" name="role" class="form-control" required>
                            <option value="employee">موظف</option>
                            <option value="admin">مدير</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeAddUserModal()">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="addNewUser()">إضافة المستخدم</button>
            </div>
        </div>
    </div>

    <script>
        // تحميل البيانات عند تحميل الصفحة
        window.addEventListener('load', function() {
            loadSystemStatistics();
            loadActivityLog();
            updateLastBackupDate();
        });

        // تحميل إحصائيات النظام
        async function loadSystemStatistics() {
            try {
                // تحميل عدد الموظفين
                const employeesResponse = await fetch('employees.php?action=statistics');
                if (employeesResponse.ok) {
                    const employeesData = await employeesResponse.json();
                    if (employeesData.success) {
                        document.getElementById('employeesCount').textContent = employeesData.data.total || 0;
                    }
                }

                // حساب إجمالي المعاملات
                const [revenuesResponse, expensesResponse, loansResponse] = await Promise.all([
                    fetch('revenues.php?action=statistics'),
                    fetch('expenses.php?action=statistics'),
                    fetch('loans.php?action=statistics')
                ]);

                let totalTransactions = 0;
                if (revenuesResponse.ok) {
                    const revenuesData = await revenuesResponse.json();
                    if (revenuesData.success && revenuesData.data.by_source) {
                        totalTransactions += revenuesData.data.by_source.reduce((sum, item) => sum + (item.count || 0), 0);
                    }
                }

                if (expensesResponse.ok) {
                    const expensesData = await expensesResponse.json();
                    if (expensesData.success && expensesData.data.by_category) {
                        totalTransactions += expensesData.data.by_category.reduce((sum, item) => sum + (item.count || 0), 0);
                    }
                }

                if (loansResponse.ok) {
                    const loansData = await loansResponse.json();
                    if (loansData.success) {
                        totalTransactions += loansData.data.total || 0;
                    }
                }

                document.getElementById('transactionsCount').textContent = totalTransactions;
                document.getElementById('databaseSize').textContent = 'تقريباً ' + Math.round(totalTransactions * 0.5) + ' كيلوبايت';

            } catch (error) {
                console.error('خطأ في تحميل إحصائيات النظام:', error);
            }
        }

        // تحميل سجل الأنشطة
        async function loadActivityLog() {
            try {
                // محاكاة سجل الأنشطة
                const activities = [
                    { user: 'مدير النظام', activity: 'تسجيل دخول', ip: '*************', date: new Date().toISOString() },
                    { user: 'مدير النظام', activity: 'إضافة موظف جديد', ip: '*************', date: new Date(Date.now() - 3600000).toISOString() },
                    { user: 'مدير النظام', activity: 'إنشاء تقرير مالي', ip: '*************', date: new Date(Date.now() - 7200000).toISOString() }
                ];

                const tableBody = document.getElementById('activityLogTable');
                tableBody.innerHTML = activities.map(activity => `
                    <tr>
                        <td>${activity.user}</td>
                        <td>${activity.activity}</td>
                        <td>${activity.ip}</td>
                        <td>${formatDateTime(activity.date)}</td>
                    </tr>
                `).join('');

            } catch (error) {
                console.error('خطأ في تحميل سجل الأنشطة:', error);
                document.getElementById('activityLogTable').innerHTML = '<tr><td colspan="4" style="text-align: center;">حدث خطأ في تحميل البيانات</td></tr>';
            }
        }

        // تغيير كلمة المرور
        function changePassword() {
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (!currentPassword || !newPassword || !confirmPassword) {
                showAlert('يرجى ملء جميع الحقول', 'warning');
                return;
            }

            if (newPassword !== confirmPassword) {
                showAlert('كلمة المرور الجديدة وتأكيدها غير متطابقين', 'danger');
                return;
            }

            if (newPassword.length < 6) {
                showAlert('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'warning');
                return;
            }

            // محاكاة تغيير كلمة المرور
            showAlert('تم تغيير كلمة المرور بنجاح', 'success');
            document.getElementById('userSettingsForm').reset();
        }

        // حفظ إعدادات النظام
        function saveSystemSettings() {
            const systemName = document.getElementById('systemName').value;
            const maxLoanAmount = document.getElementById('maxLoanAmount').value;
            const maxLoanPercentage = document.getElementById('maxLoanPercentage').value;

            // محاكاة حفظ الإعدادات
            showAlert('تم حفظ إعدادات النظام بنجاح', 'success');
        }

        // إضافة مستخدم جديد
        function showAddUserModal() {
            document.getElementById('addUserModal').style.display = 'flex';
        }

        function closeAddUserModal() {
            document.getElementById('addUserModal').style.display = 'none';
            document.getElementById('addUserForm').reset();
        }

        function addNewUser() {
            const form = document.getElementById('addUserForm');
            const formData = new FormData(form);

            // التحقق من البيانات
            if (!formData.get('username') || !formData.get('full_name') || !formData.get('password')) {
                showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            // محاكاة إضافة المستخدم
            showAlert('تم إضافة المستخدم بنجاح', 'success');
            closeAddUserModal();
            
            // تحديث عدد المستخدمين
            const currentCount = parseInt(document.getElementById('usersCount').textContent);
            document.getElementById('usersCount').textContent = currentCount + 1;
        }

        // إنشاء نسخة احتياطية
        function createBackup() {
            if (confirm('هل أنت متأكد من إنشاء نسخة احتياطية؟')) {
                // محاكاة إنشاء النسخة الاحتياطية
                showAlert('جاري إنشاء النسخة الاحتياطية...', 'info');
                
                setTimeout(() => {
                    showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
                    updateLastBackupDate();
                }, 2000);
            }
        }

        // تحديث تاريخ آخر نسخة احتياطية
        function updateLastBackupDate() {
            const now = new Date();
            document.getElementById('lastBackupDate').textContent = formatDateTime(now.toISOString());
        }

        // عرض نافذة استعادة النسخة الاحتياطية
        function showRestoreModal() {
            alert('ميزة استعادة النسخة الاحتياطية ستكون متاحة قريباً');
        }

        // عرض سجل الأنشطة
        function viewActivityLog() {
            alert('سجل الأنشطة معروض في الأسفل');
        }

        // إدارة الصلاحيات
        function managePermissions() {
            alert('ميزة إدارة الصلاحيات ستكون متاحة قريباً');
        }

        // وظائف مساعدة
        function formatDateTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('ar-DZ');
        }

        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alert.style.position = 'fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.style.minWidth = '300px';
            
            document.body.appendChild(alert);
            
            setTimeout(() => {
                document.body.removeChild(alert);
            }, 5000);
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                window.location.href = 'index.html';
            }
        }
    </script>

    <style>
        .stat-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .stat-item:last-child {
            border-bottom: none;
        }
        
        .stat-item span:last-child {
            font-weight: bold;
            color: #333;
        }
        
        .form-control-static {
            padding: 7px 0;
            margin-bottom: 0;
            min-height: 34px;
        }
    </style>
</body>
</html>
