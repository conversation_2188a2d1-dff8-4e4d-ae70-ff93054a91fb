<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العمرة - لجنة الخدمات الاجتماعية</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="dashboard">
    <!-- شريط التنقل العلوي -->
    <nav class="navbar">
        <div class="navbar-content">
            <a href="dashboard.html" class="navbar-brand">
                <i class="fas fa-users"></i>
                لجنة الخدمات الاجتماعية - ولاية الجلفة
            </a>
            <div class="navbar-user">
                <div class="user-info">
                    <i class="fas fa-user"></i>
                    <span id="userName">مرحباً، مدير النظام</span>
                </div>
                <button class="btn btn-logout" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>
    </nav>

    <!-- الشريط الجانبي -->
    <aside class="sidebar">
        <ul class="sidebar-menu">
            <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i>لوحة التحكم</a></li>
            <li><a href="employees.html"><i class="fas fa-users"></i>إدارة الموظفين</a></li>
            <li><a href="revenues.html"><i class="fas fa-arrow-up"></i>الإيرادات</a></li>
            <li><a href="expenses.html"><i class="fas fa-arrow-down"></i>المصروفات</a></li>
            <li><a href="loans.html"><i class="fas fa-money-bill-wave"></i>السلفات المالية</a></li>
            <li><a href="umrah.html" class="active"><i class="fas fa-kaaba"></i>العمرة</a></li>
            <li><a href="vacation.html"><i class="fas fa-umbrella-beach"></i>الاصطياف</a></li>
            <li><a href="reports.html"><i class="fas fa-chart-bar"></i>التقارير والإحصائيات</a></li>
            <li><a href="settings.html"><i class="fas fa-cog"></i>الإعدادات</a></li>
        </ul>
    </aside>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">إدارة العمرة</h1>
            <p class="page-subtitle">إدارة طلبات العمرة للموظفين</p>
        </div>

        <!-- إحصائيات العمرة -->
        <div class="stats-grid" style="margin-bottom: 30px;">
            <div class="stat-card">
                <div class="stat-icon blue">
                    <i class="fas fa-kaaba"></i>
                </div>
                <div class="stat-number" id="totalApplications">0</div>
                <div class="stat-label">إجمالي الطلبات</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon green">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number" id="approvedApplications">0</div>
                <div class="stat-label">الطلبات المقبولة</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon orange">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number" id="pendingApplications">0</div>
                <div class="stat-label">قيد الدراسة</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon purple">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-number currency" id="totalCost">0</div>
                <div class="stat-label">إجمالي التكلفة</div>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div style="margin-bottom: 20px;">
            <button class="btn btn-primary" onclick="showAddUmrahModal()">
                <i class="fas fa-plus"></i>
                طلب عمرة جديد
            </button>
            <button class="btn btn-success" onclick="exportUmrahData()">
                <i class="fas fa-download"></i>
                تصدير البيانات
            </button>
            <button class="btn btn-info" onclick="showLottery()">
                <i class="fas fa-random"></i>
                إجراء قرعة
            </button>
        </div>

        <!-- البحث والفلترة -->
        <div class="card" style="margin-bottom: 20px;">
            <div class="card-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="searchUmrah">البحث</label>
                        <input type="text" id="searchUmrah" class="form-control" placeholder="البحث بالاسم أو رقم التسجيل">
                    </div>
                    <div class="form-group">
                        <label for="filterStatus">الحالة</label>
                        <select id="filterStatus" class="form-control">
                            <option value="">جميع الحالات</option>
                            <option value="قيد الدراسة">قيد الدراسة</option>
                            <option value="مقبول">مقبول</option>
                            <option value="مرفوض">مرفوض</option>
                            <option value="مكتمل">مكتمل</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="filterPreviousUmrah">الاستفادة السابقة</label>
                        <select id="filterPreviousUmrah" class="form-control">
                            <option value="">الكل</option>
                            <option value="0">لم يستفد من قبل</option>
                            <option value="1">استفاد مرة واحدة</option>
                            <option value="2+">استفاد أكثر من مرة</option>
                        </select>
                    </div>
                    <div class="form-group" style="display: flex; align-items: end;">
                        <button class="btn btn-info" onclick="filterUmrah()">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول طلبات العمرة -->
        <div class="table-container">
            <div class="table-header">
                <h3 class="table-title">قائمة طلبات العمرة</h3>
                <button class="btn btn-primary btn-sm" onclick="loadUmrahApplications()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث
                </button>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>الموظف</th>
                        <th>الرتبة</th>
                        <th>تاريخ التوظيف</th>
                        <th>عدد مرات العمرة السابقة</th>
                        <th>مرافق</th>
                        <th>التكلفة الإجمالية</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="umrahTable">
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 40px;">
                            <div class="loading">
                                <div class="spinner"></div>
                                <p>جاري تحميل البيانات...</p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </main>

    <!-- نافذة إضافة/تعديل طلب عمرة -->
    <div id="umrahModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="umrahModalTitle">طلب عمرة جديد</h3>
                <button class="btn-close" onclick="closeUmrahModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="umrahForm">
                    <input type="hidden" id="umrahId" name="id">
                    
                    <div class="form-group">
                        <label for="umrahEmployee">الموظف *</label>
                        <select id="umrahEmployee" name="employee_id" class="form-control" required onchange="updateEmployeeInfo()">
                            <option value="">اختر الموظف</option>
                        </select>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="previousUmrahCount">عدد مرات العمرة السابقة</label>
                            <input type="number" id="previousUmrahCount" name="previous_umrah_count" class="form-control" min="0" value="0">
                        </div>
                        <div class="form-group">
                            <label for="hasCompanion">يوجد مرافق</label>
                            <select id="hasCompanion" name="has_companion" class="form-control" onchange="toggleCompanionField()">
                                <option value="0">لا</option>
                                <option value="1">نعم</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group" id="companionNameGroup" style="display: none;">
                        <label for="companionName">اسم المرافق</label>
                        <input type="text" id="companionName" name="companion_name" class="form-control">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="totalCost">التكلفة الإجمالية (د.ج)</label>
                            <input type="number" id="totalCost" name="total_cost" class="form-control" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="committeeContribution">مساهمة اللجنة (د.ج)</label>
                            <input type="number" id="committeeContribution" name="committee_contribution" class="form-control" step="0.01">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="employeeContribution">مساهمة الموظف (د.ج)</label>
                        <input type="number" id="employeeContribution" name="employee_contribution" class="form-control" step="0.01" readonly>
                    </div>
                    
                    <div class="form-group">
                        <label for="travelAgency">وكالة السفر</label>
                        <input type="text" id="travelAgency" name="travel_agency" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label for="umrahAttachments">المرفقات</label>
                        <input type="file" id="umrahAttachments" name="attachments" class="form-control" multiple accept=".pdf,.jpg,.jpeg,.png">
                        <small class="form-text">جواز السفر، شهادة عمل، شهادة عائلية</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeUmrahModal()">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveUmrahApplication()">حفظ الطلب</button>
            </div>
        </div>
    </div>

    <script>
        let umrahApplications = [];
        let employees = [];

        // تحميل البيانات عند تحميل الصفحة
        window.addEventListener('load', function() {
            loadEmployees();
            loadUmrahApplications();
            loadStatistics();
        });

        // تحميل قائمة الموظفين
        async function loadEmployees() {
            try {
                const response = await fetch('employees.php?action=list');
                const result = await response.json();
                
                if (result.success) {
                    employees = result.data;
                    populateEmployeeSelect();
                }
            } catch (error) {
                console.error('خطأ في تحميل الموظفين:', error);
            }
        }

        // ملء قائمة الموظفين
        function populateEmployeeSelect() {
            const select = document.getElementById('umrahEmployee');
            select.innerHTML = '<option value="">اختر الموظف</option>';
            
            employees.forEach(employee => {
                const option = document.createElement('option');
                option.value = employee.id;
                option.textContent = `${employee.full_name} - ${employee.registration_number}`;
                option.dataset.rank = employee.rank_position;
                option.dataset.hireDate = employee.hire_date;
                select.appendChild(option);
            });
        }

        // تحديث معلومات الموظف
        function updateEmployeeInfo() {
            const select = document.getElementById('umrahEmployee');
            const selectedOption = select.options[select.selectedIndex];
            
            if (selectedOption.value) {
                // يمكن إضافة منطق لتحديث الحقول الأخرى بناءً على بيانات الموظف
            }
        }

        // تبديل حقل المرافق
        function toggleCompanionField() {
            const hasCompanion = document.getElementById('hasCompanion').value;
            const companionGroup = document.getElementById('companionNameGroup');
            
            if (hasCompanion === '1') {
                companionGroup.style.display = 'block';
            } else {
                companionGroup.style.display = 'none';
                document.getElementById('companionName').value = '';
            }
        }

        // حساب مساهمة الموظف تلقائياً
        document.getElementById('totalCost').addEventListener('input', calculateEmployeeContribution);
        document.getElementById('committeeContribution').addEventListener('input', calculateEmployeeContribution);

        function calculateEmployeeContribution() {
            const totalCost = parseFloat(document.getElementById('totalCost').value) || 0;
            const committeeContribution = parseFloat(document.getElementById('committeeContribution').value) || 0;
            
            const employeeContribution = Math.max(0, totalCost - committeeContribution);
            document.getElementById('employeeContribution').value = employeeContribution.toFixed(2);
        }

        // تحميل طلبات العمرة
        async function loadUmrahApplications() {
            try {
                const response = await fetch('umrah.php?action=list');
                const result = await response.json();
                
                if (result.success) {
                    umrahApplications = result.data;
                    displayUmrahApplications(umrahApplications);
                } else {
                    showAlert('خطأ في تحميل البيانات: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('خطأ في تحميل طلبات العمرة:', error);
                showAlert('حدث خطأ في تحميل البيانات', 'danger');
            }
        }

        // تحميل الإحصائيات
        async function loadStatistics() {
            try {
                const response = await fetch('umrah.php?action=statistics');
                const result = await response.json();
                
                if (result.success) {
                    const stats = result.data;
                    document.getElementById('totalApplications').textContent = stats.total || 0;
                    document.getElementById('approvedApplications').textContent = stats.approved || 0;
                    document.getElementById('pendingApplications').textContent = stats.pending || 0;
                    document.getElementById('totalCost').textContent = formatCurrency(stats.total_cost || 0);
                }
            } catch (error) {
                console.error('خطأ في تحميل الإحصائيات:', error);
            }
        }

        // عرض طلبات العمرة في الجدول
        function displayUmrahApplications(applicationsList) {
            const tableBody = document.getElementById('umrahTable');
            
            if (applicationsList.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="8" style="text-align: center; padding: 40px;">لا توجد طلبات عمرة</td></tr>';
                return;
            }
            
            tableBody.innerHTML = applicationsList.map(app => `
                <tr>
                    <td>${app.employee_name || 'غير محدد'}</td>
                    <td>${app.rank_position || '-'}</td>
                    <td>${formatDate(app.hire_date)}</td>
                    <td>${app.previous_umrah_count}</td>
                    <td>${app.has_companion ? (app.companion_name || 'نعم') : 'لا'}</td>
                    <td class="currency">${formatCurrency(app.total_cost || 0)}</td>
                    <td><span class="status status-${getStatusClass(app.status)}">${app.status}</span></td>
                    <td>
                        <button class="btn btn-info btn-sm" onclick="viewUmrahDetails(${app.id})" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-warning btn-sm" onclick="editUmrahApplication(${app.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-success btn-sm" onclick="approveUmrahApplication(${app.id})" title="موافقة">
                            <i class="fas fa-check"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // فلترة طلبات العمرة
        function filterUmrah() {
            const searchTerm = document.getElementById('searchUmrah').value.toLowerCase();
            const statusFilter = document.getElementById('filterStatus').value;
            const previousUmrahFilter = document.getElementById('filterPreviousUmrah').value;
            
            const filteredApplications = umrahApplications.filter(app => {
                const matchesSearch = (app.employee_name || '').toLowerCase().includes(searchTerm) || 
                                    (app.registration_number || '').toLowerCase().includes(searchTerm);
                const matchesStatus = !statusFilter || app.status === statusFilter;
                
                let matchesPrevious = true;
                if (previousUmrahFilter === '0') {
                    matchesPrevious = app.previous_umrah_count == 0;
                } else if (previousUmrahFilter === '1') {
                    matchesPrevious = app.previous_umrah_count == 1;
                } else if (previousUmrahFilter === '2+') {
                    matchesPrevious = app.previous_umrah_count >= 2;
                }
                
                return matchesSearch && matchesStatus && matchesPrevious;
            });
            
            displayUmrahApplications(filteredApplications);
        }

        // إضافة مستمعين للبحث المباشر
        document.getElementById('searchUmrah').addEventListener('input', filterUmrah);
        document.getElementById('filterStatus').addEventListener('change', filterUmrah);
        document.getElementById('filterPreviousUmrah').addEventListener('change', filterUmrah);

        // عرض نافذة إضافة طلب عمرة
        function showAddUmrahModal() {
            document.getElementById('umrahModalTitle').textContent = 'طلب عمرة جديد';
            document.getElementById('umrahForm').reset();
            document.getElementById('umrahId').value = '';
            toggleCompanionField();
            document.getElementById('umrahModal').style.display = 'flex';
        }

        // حفظ طلب العمرة
        async function saveUmrahApplication() {
            const form = document.getElementById('umrahForm');
            const formData = new FormData(form);
            
            const action = document.getElementById('umrahId').value ? 'update' : 'create';
            formData.append('action', action);
            
            try {
                const response = await fetch('umrah.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert(result.message, 'success');
                    closeUmrahModal();
                    loadUmrahApplications();
                    loadStatistics();
                } else {
                    showAlert('خطأ: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('خطأ في حفظ طلب العمرة:', error);
                showAlert('حدث خطأ في حفظ البيانات', 'danger');
            }
        }

        // إغلاق النافذة المنبثقة
        function closeUmrahModal() {
            document.getElementById('umrahModal').style.display = 'none';
        }

        // وظائف مساعدة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-DZ', {
                style: 'decimal',
                minimumFractionDigits: 2
            }).format(amount);
        }

        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-DZ');
        }

        function getStatusClass(status) {
            switch (status) {
                case 'مقبول': 
                case 'مكتمل': return 'approved';
                case 'مرفوض': return 'rejected';
                default: return 'pending';
            }
        }

        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alert.style.position = 'fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.style.minWidth = '300px';
            
            document.body.appendChild(alert);
            
            setTimeout(() => {
                document.body.removeChild(alert);
            }, 5000);
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                window.location.href = 'index.html';
            }
        }
    </script>
</body>
</html>
