<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموظفين - لجنة الخدمات الاجتماعية</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="dashboard">
    <!-- شريط التنقل العلوي -->
    <nav class="navbar">
        <div class="navbar-content">
            <a href="dashboard.html" class="navbar-brand">
                <i class="fas fa-users"></i>
                لجنة الخدمات الاجتماعية - ولاية الجلفة
            </a>
            <div class="navbar-user">
                <div class="user-info">
                    <i class="fas fa-user"></i>
                    <span id="userName">مرحباً</span>
                </div>
                <button class="btn btn-logout" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>
    </nav>

    <!-- الشريط الجانبي -->
    <aside class="sidebar">
        <ul class="sidebar-menu">
            <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i>لوحة التحكم</a></li>
            <li><a href="employees.html" class="active"><i class="fas fa-users"></i>إدارة الموظفين</a></li>
            <li><a href="revenues.html"><i class="fas fa-arrow-up"></i>الإيرادات</a></li>
            <li><a href="expenses.html"><i class="fas fa-arrow-down"></i>المصروفات</a></li>
            <li><a href="loans.html"><i class="fas fa-money-bill-wave"></i>السلفات المالية</a></li>
            <li><a href="umrah.html"><i class="fas fa-kaaba"></i>العمرة</a></li>
            <li><a href="vacation.html"><i class="fas fa-umbrella-beach"></i>الاصطياف</a></li>
            <li><a href="reports.html"><i class="fas fa-chart-bar"></i>التقارير والإحصائيات</a></li>
            <li><a href="settings.html"><i class="fas fa-cog"></i>الإعدادات</a></li>
        </ul>
    </aside>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">إدارة الموظفين</h1>
            <p class="page-subtitle">إدارة بيانات موظفي ولاية الجلفة</p>
        </div>

        <!-- أزرار الإجراءات -->
        <div style="margin-bottom: 20px;">
            <button class="btn btn-primary" onclick="showAddEmployeeModal()">
                <i class="fas fa-plus"></i>
                إضافة موظف جديد
            </button>
            <button class="btn btn-success" onclick="exportEmployees()">
                <i class="fas fa-download"></i>
                تصدير البيانات
            </button>
        </div>

        <!-- البحث والفلترة -->
        <div class="card" style="margin-bottom: 20px;">
            <div class="card-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="searchEmployee">البحث</label>
                        <input type="text" id="searchEmployee" class="form-control" placeholder="البحث بالاسم أو رقم التسجيل">
                    </div>
                    <div class="form-group">
                        <label for="filterRank">الرتبة/المنصب</label>
                        <select id="filterRank" class="form-control">
                            <option value="">جميع الرتب</option>
                            <option value="مدير">مدير</option>
                            <option value="رئيس مصلحة">رئيس مصلحة</option>
                            <option value="موظف">موظف</option>
                            <option value="عون">عون</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="filterMaritalStatus">الحالة العائلية</label>
                        <select id="filterMaritalStatus" class="form-control">
                            <option value="">جميع الحالات</option>
                            <option value="متزوج">متزوج</option>
                            <option value="أعزب">أعزب</option>
                            <option value="مطلق">مطلق</option>
                            <option value="أرمل">أرمل</option>
                        </select>
                    </div>
                    <div class="form-group" style="display: flex; align-items: end;">
                        <button class="btn btn-info" onclick="filterEmployees()">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول الموظفين -->
        <div class="table-container">
            <div class="table-header">
                <h3 class="table-title">قائمة الموظفين</h3>
                <button class="btn btn-primary btn-sm" onclick="loadEmployees()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث
                </button>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>رقم التسجيل</th>
                        <th>الاسم الكامل</th>
                        <th>الرتبة/المنصب</th>
                        <th>الجنس</th>
                        <th>الحالة العائلية</th>
                        <th>عدد الأولاد</th>
                        <th>الراتب</th>
                        <th>تاريخ التوظيف</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="employeesTable">
                    <tr>
                        <td colspan="9" style="text-align: center; padding: 40px;">
                            <div class="loading">
                                <div class="spinner"></div>
                                <p>جاري تحميل البيانات...</p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </main>

    <!-- نافذة إضافة/تعديل موظف -->
    <div id="employeeModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">إضافة موظف جديد</h3>
                <button class="btn-close" onclick="closeEmployeeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="employeeForm">
                    <input type="hidden" id="employeeId" name="id">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="registrationNumber">رقم التسجيل *</label>
                            <input type="text" id="registrationNumber" name="registration_number" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="fullName">الاسم الكامل *</label>
                            <input type="text" id="fullName" name="full_name" class="form-control" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="rankPosition">الرتبة/المنصب *</label>
                            <select id="rankPosition" name="rank_position" class="form-control" required>
                                <option value="">اختر الرتبة</option>
                                <option value="مدير">مدير</option>
                                <option value="رئيس مصلحة">رئيس مصلحة</option>
                                <option value="موظف">موظف</option>
                                <option value="عون">عون</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="gender">الجنس *</label>
                            <select id="gender" name="gender" class="form-control" required>
                                <option value="">اختر الجنس</option>
                                <option value="ذكر">ذكر</option>
                                <option value="أنثى">أنثى</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="maritalStatus">الحالة العائلية *</label>
                            <select id="maritalStatus" name="marital_status" class="form-control" required>
                                <option value="">اختر الحالة</option>
                                <option value="متزوج">متزوج</option>
                                <option value="أعزب">أعزب</option>
                                <option value="مطلق">مطلق</option>
                                <option value="أرمل">أرمل</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="childrenCount">عدد الأولاد</label>
                            <input type="number" id="childrenCount" name="children_count" class="form-control" min="0" value="0">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="childrenNames">أسماء الأولاد</label>
                        <textarea id="childrenNames" name="children_names" class="form-control" rows="3" placeholder="اسم الطفل الأول، اسم الطفل الثاني..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="childrenLevels">مستويات الأولاد الدراسية</label>
                        <textarea id="childrenLevels" name="children_levels" class="form-control" rows="3" placeholder="السنة الأولى ابتدائي، السنة الثالثة متوسط..."></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="salary">مبلغ الراتب (د.ج) *</label>
                            <input type="number" id="salary" name="salary" class="form-control" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label for="hireDate">تاريخ التوظيف *</label>
                            <input type="date" id="hireDate" name="hire_date" class="form-control" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="bankAccount">معلومات الحساب البنكي</label>
                        <input type="text" id="bankAccount" name="bank_account" class="form-control" placeholder="رقم الحساب البنكي">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeEmployeeModal()">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveEmployee()">حفظ</button>
            </div>
        </div>
    </div>

    <script>
        let employees = [];
        let currentEmployee = null;

        // تحميل البيانات عند تحميل الصفحة
        window.addEventListener('load', function() {
            checkAuthentication();
            loadEmployees();
        });

        // التحقق من المصادقة
        async function checkAuthentication() {
            try {
                const response = await fetch('auth.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=get_current_user'
                });
                
                const result = await response.json();
                
                if (result.success && result.user) {
                    document.getElementById('userName').textContent = `مرحباً، ${result.user.full_name}`;
                } else {
                    window.location.href = 'index.html';
                }
            } catch (error) {
                console.error('خطأ في التحقق من المصادقة:', error);
                window.location.href = 'index.html';
            }
        }

        // تحميل قائمة الموظفين
        async function loadEmployees() {
            try {
                const response = await fetch('employees.php?action=list');
                const result = await response.json();
                
                if (result.success) {
                    employees = result.data;
                    displayEmployees(employees);
                } else {
                    showAlert('خطأ في تحميل البيانات: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('خطأ في تحميل الموظفين:', error);
                showAlert('حدث خطأ في تحميل البيانات', 'danger');
            }
        }

        // عرض الموظفين في الجدول
        function displayEmployees(employeesList) {
            const tableBody = document.getElementById('employeesTable');
            
            if (employeesList.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="9" style="text-align: center; padding: 40px;">لا توجد بيانات موظفين</td></tr>';
                return;
            }
            
            tableBody.innerHTML = employeesList.map(employee => `
                <tr>
                    <td>${employee.registration_number}</td>
                    <td>${employee.full_name}</td>
                    <td>${employee.rank_position}</td>
                    <td>${employee.gender}</td>
                    <td>${employee.marital_status}</td>
                    <td>${employee.children_count}</td>
                    <td class="currency">${formatCurrency(employee.salary)}</td>
                    <td>${formatDate(employee.hire_date)}</td>
                    <td>
                        <button class="btn btn-info btn-sm" onclick="viewEmployee(${employee.id})" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-warning btn-sm" onclick="editEmployee(${employee.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="deleteEmployee(${employee.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // فلترة الموظفين
        function filterEmployees() {
            const searchTerm = document.getElementById('searchEmployee').value.toLowerCase();
            const rankFilter = document.getElementById('filterRank').value;
            const maritalFilter = document.getElementById('filterMaritalStatus').value;
            
            const filteredEmployees = employees.filter(employee => {
                const matchesSearch = employee.full_name.toLowerCase().includes(searchTerm) || 
                                    employee.registration_number.toLowerCase().includes(searchTerm);
                const matchesRank = !rankFilter || employee.rank_position === rankFilter;
                const matchesMarital = !maritalFilter || employee.marital_status === maritalFilter;
                
                return matchesSearch && matchesRank && matchesMarital;
            });
            
            displayEmployees(filteredEmployees);
        }

        // إضافة مستمع للبحث المباشر
        document.getElementById('searchEmployee').addEventListener('input', filterEmployees);
        document.getElementById('filterRank').addEventListener('change', filterEmployees);
        document.getElementById('filterMaritalStatus').addEventListener('change', filterEmployees);

        // عرض نافذة إضافة موظف
        function showAddEmployeeModal() {
            currentEmployee = null;
            document.getElementById('modalTitle').textContent = 'إضافة موظف جديد';
            document.getElementById('employeeForm').reset();
            document.getElementById('employeeId').value = '';
            document.getElementById('employeeModal').style.display = 'flex';
        }

        // تعديل موظف
        function editEmployee(id) {
            currentEmployee = employees.find(emp => emp.id == id);
            if (!currentEmployee) return;
            
            document.getElementById('modalTitle').textContent = 'تعديل بيانات الموظف';
            document.getElementById('employeeId').value = currentEmployee.id;
            document.getElementById('registrationNumber').value = currentEmployee.registration_number;
            document.getElementById('fullName').value = currentEmployee.full_name;
            document.getElementById('rankPosition').value = currentEmployee.rank_position;
            document.getElementById('gender').value = currentEmployee.gender;
            document.getElementById('maritalStatus').value = currentEmployee.marital_status;
            document.getElementById('childrenCount').value = currentEmployee.children_count;
            document.getElementById('childrenNames').value = currentEmployee.children_names || '';
            document.getElementById('childrenLevels').value = currentEmployee.children_levels || '';
            document.getElementById('salary').value = currentEmployee.salary;
            document.getElementById('hireDate').value = currentEmployee.hire_date;
            document.getElementById('bankAccount').value = currentEmployee.bank_account || '';
            
            document.getElementById('employeeModal').style.display = 'flex';
        }

        // حفظ الموظف
        async function saveEmployee() {
            const form = document.getElementById('employeeForm');
            const formData = new FormData(form);
            
            const action = document.getElementById('employeeId').value ? 'update' : 'create';
            formData.append('action', action);
            
            try {
                const response = await fetch('employees.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert(result.message, 'success');
                    closeEmployeeModal();
                    loadEmployees();
                } else {
                    showAlert('خطأ: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('خطأ في حفظ الموظف:', error);
                showAlert('حدث خطأ في حفظ البيانات', 'danger');
            }
        }

        // حذف موظف
        async function deleteEmployee(id) {
            if (!confirm('هل أنت متأكد من حذف هذا الموظف؟')) return;
            
            try {
                const formData = new FormData();
                formData.append('action', 'delete');
                formData.append('id', id);
                
                const response = await fetch('employees.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert(result.message, 'success');
                    loadEmployees();
                } else {
                    showAlert('خطأ: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('خطأ في حذف الموظف:', error);
                showAlert('حدث خطأ في حذف الموظف', 'danger');
            }
        }

        // عرض تفاصيل الموظف
        function viewEmployee(id) {
            const employee = employees.find(emp => emp.id == id);
            if (!employee) return;
            
            alert(`تفاصيل الموظف:
الاسم: ${employee.full_name}
رقم التسجيل: ${employee.registration_number}
الرتبة: ${employee.rank_position}
الراتب: ${formatCurrency(employee.salary)} د.ج
عدد الأولاد: ${employee.children_count}`);
        }

        // إغلاق النافذة المنبثقة
        function closeEmployeeModal() {
            document.getElementById('employeeModal').style.display = 'none';
        }

        // تصدير البيانات
        function exportEmployees() {
            // تحويل البيانات إلى CSV
            const headers = ['رقم التسجيل', 'الاسم الكامل', 'الرتبة', 'الجنس', 'الحالة العائلية', 'عدد الأولاد', 'الراتب', 'تاريخ التوظيف'];
            const csvContent = [
                headers.join(','),
                ...employees.map(emp => [
                    emp.registration_number,
                    emp.full_name,
                    emp.rank_position,
                    emp.gender,
                    emp.marital_status,
                    emp.children_count,
                    emp.salary,
                    emp.hire_date
                ].join(','))
            ].join('\n');
            
            // تحميل الملف
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = 'employees_' + new Date().toISOString().split('T')[0] + '.csv';
            link.click();
        }

        // وظائف مساعدة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-DZ', {
                style: 'decimal',
                minimumFractionDigits: 2
            }).format(amount);
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-DZ');
        }

        function showAlert(message, type) {
            // إنشاء تنبيه مؤقت
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alert.style.position = 'fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.style.minWidth = '300px';
            
            document.body.appendChild(alert);
            
            setTimeout(() => {
                document.body.removeChild(alert);
            }, 5000);
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                window.location.href = 'auth.php?action=logout';
            }
        }
    </script>

    <style>
        /* تنسيقات النافذة المنبثقة */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: white;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .modal-header {
            padding: 20px 25px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            color: #333;
        }

        .btn-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        .modal-body {
            padding: 25px;
        }

        .modal-footer {
            padding: 20px 25px;
            border-top: 1px solid #e9ecef;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }
    </style>
</body>
</html>
