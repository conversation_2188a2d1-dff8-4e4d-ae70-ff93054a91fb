<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الإيرادات - لجنة الخدمات الاجتماعية</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="dashboard">
    <!-- شريط التنقل العلوي -->
    <nav class="navbar">
        <div class="navbar-content">
            <a href="dashboard.html" class="navbar-brand">
                <i class="fas fa-users"></i>
                لجنة الخدمات الاجتماعية - ولاية الجلفة
            </a>
            <div class="navbar-user">
                <div class="user-info">
                    <i class="fas fa-user"></i>
                    <span id="userName">مرحباً</span>
                </div>
                <button class="btn btn-logout" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>
    </nav>

    <!-- الشريط الجانبي -->
    <aside class="sidebar">
        <ul class="sidebar-menu">
            <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i>لوحة التحكم</a></li>
            <li><a href="employees.html"><i class="fas fa-users"></i>إدارة الموظفين</a></li>
            <li><a href="revenues.html" class="active"><i class="fas fa-arrow-up"></i>الإيرادات</a></li>
            <li><a href="expenses.html"><i class="fas fa-arrow-down"></i>المصروفات</a></li>
            <li><a href="loans.html"><i class="fas fa-money-bill-wave"></i>السلفات المالية</a></li>
            <li><a href="umrah.html"><i class="fas fa-kaaba"></i>العمرة</a></li>
            <li><a href="vacation.html"><i class="fas fa-umbrella-beach"></i>الاصطياف</a></li>
            <li><a href="reports.html"><i class="fas fa-chart-bar"></i>التقارير والإحصائيات</a></li>
            <li><a href="settings.html"><i class="fas fa-cog"></i>الإعدادات</a></li>
        </ul>
    </aside>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">إدارة الإيرادات</h1>
            <p class="page-subtitle">إدارة مصادر الدخل للجنة الخدمات الاجتماعية</p>
        </div>

        <!-- إحصائيات الإيرادات -->
        <div class="stats-grid" style="margin-bottom: 30px;">
            <div class="stat-card">
                <div class="stat-icon green">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="stat-number currency" id="totalRevenues">0</div>
                <div class="stat-label">إجمالي الإيرادات</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon blue">
                    <i class="fas fa-university"></i>
                </div>
                <div class="stat-number currency" id="governmentSupport">0</div>
                <div class="stat-label">الإعانات الحكومية</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon orange">
                    <i class="fas fa-hand-holding-heart"></i>
                </div>
                <div class="stat-number currency" id="donations">0</div>
                <div class="stat-label">التبرعات</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon purple">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-number currency" id="bankInterest">0</div>
                <div class="stat-label">الفوائد البنكية</div>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div style="margin-bottom: 20px;">
            <button class="btn btn-primary" onclick="showAddRevenueModal()">
                <i class="fas fa-plus"></i>
                إضافة إيراد جديد
            </button>
            <button class="btn btn-success" onclick="exportRevenues()">
                <i class="fas fa-download"></i>
                تصدير البيانات
            </button>
            <button class="btn btn-info" onclick="showMonthlyReport()">
                <i class="fas fa-chart-bar"></i>
                التقرير الشهري
            </button>
        </div>

        <!-- البحث والفلترة -->
        <div class="card" style="margin-bottom: 20px;">
            <div class="card-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="searchRevenue">البحث</label>
                        <input type="text" id="searchRevenue" class="form-control" placeholder="البحث في الوصف">
                    </div>
                    <div class="form-group">
                        <label for="filterSource">مصدر الإيراد</label>
                        <select id="filterSource" class="form-control">
                            <option value="">جميع المصادر</option>
                            <option value="إعانة الدولة">إعانة الدولة</option>
                            <option value="فوائد بنكية">فوائد بنكية</option>
                            <option value="تبرعات">تبرعات</option>
                            <option value="دعم خارجي">دعم خارجي</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="filterDateFrom">من تاريخ</label>
                        <input type="date" id="filterDateFrom" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="filterDateTo">إلى تاريخ</label>
                        <input type="date" id="filterDateTo" class="form-control">
                    </div>
                    <div class="form-group" style="display: flex; align-items: end;">
                        <button class="btn btn-info" onclick="filterRevenues()">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول الإيرادات -->
        <div class="table-container">
            <div class="table-header">
                <h3 class="table-title">قائمة الإيرادات</h3>
                <button class="btn btn-primary btn-sm" onclick="loadRevenues()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث
                </button>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>مصدر الإيراد</th>
                        <th>المبلغ</th>
                        <th>الوصف</th>
                        <th>المسجل بواسطة</th>
                        <th>تاريخ التسجيل</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="revenuesTable">
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 40px;">
                            <div class="loading">
                                <div class="spinner"></div>
                                <p>جاري تحميل البيانات...</p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </main>

    <!-- نافذة إضافة/تعديل إيراد -->
    <div id="revenueModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="revenueModalTitle">إضافة إيراد جديد</h3>
                <button class="btn-close" onclick="closeRevenueModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="revenueForm">
                    <input type="hidden" id="revenueId" name="id">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="revenueDate">التاريخ *</label>
                            <input type="date" id="revenueDate" name="date" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="sourceType">مصدر الإيراد *</label>
                            <select id="sourceType" name="source_type" class="form-control" required>
                                <option value="">اختر المصدر</option>
                                <option value="إعانة الدولة">إعانة الدولة</option>
                                <option value="فوائد بنكية">فوائد بنكية</option>
                                <option value="تبرعات">تبرعات</option>
                                <option value="دعم خارجي">دعم خارجي</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="revenueAmount">المبلغ (د.ج) *</label>
                        <input type="number" id="revenueAmount" name="amount" class="form-control" step="0.01" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="revenueDescription">الوصف</label>
                        <textarea id="revenueDescription" name="description" class="form-control" rows="3" placeholder="تفاصيل الإيراد..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeRevenueModal()">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveRevenue()">حفظ</button>
            </div>
        </div>
    </div>

    <script>
        let revenues = [];
        let currentRevenue = null;

        // تحميل البيانات عند تحميل الصفحة
        window.addEventListener('load', function() {
            checkAuthentication();
            loadRevenues();
            loadStatistics();
            
            // تعيين التاريخ الحالي كافتراضي
            document.getElementById('revenueDate').value = new Date().toISOString().split('T')[0];
        });

        // التحقق من المصادقة
        async function checkAuthentication() {
            try {
                const response = await fetch('auth.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=get_current_user'
                });
                
                const result = await response.json();
                
                if (result.success && result.user) {
                    document.getElementById('userName').textContent = `مرحباً، ${result.user.full_name}`;
                } else {
                    window.location.href = 'index.html';
                }
            } catch (error) {
                console.error('خطأ في التحقق من المصادقة:', error);
                window.location.href = 'index.html';
            }
        }

        // تحميل قائمة الإيرادات
        async function loadRevenues() {
            try {
                const response = await fetch('revenues.php?action=list');
                const result = await response.json();
                
                if (result.success) {
                    revenues = result.data;
                    displayRevenues(revenues);
                } else {
                    showAlert('خطأ في تحميل البيانات: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('خطأ في تحميل الإيرادات:', error);
                showAlert('حدث خطأ في تحميل البيانات', 'danger');
            }
        }

        // تحميل الإحصائيات
        async function loadStatistics() {
            try {
                const response = await fetch('revenues.php?action=statistics');
                const result = await response.json();
                
                if (result.success) {
                    const stats = result.data;
                    document.getElementById('totalRevenues').textContent = formatCurrency(stats.total || 0);
                    document.getElementById('governmentSupport').textContent = formatCurrency(stats.government_support || 0);
                    document.getElementById('donations').textContent = formatCurrency(stats.donations || 0);
                    document.getElementById('bankInterest').textContent = formatCurrency(stats.bank_interest || 0);
                }
            } catch (error) {
                console.error('خطأ في تحميل الإحصائيات:', error);
            }
        }

        // عرض الإيرادات في الجدول
        function displayRevenues(revenuesList) {
            const tableBody = document.getElementById('revenuesTable');
            
            if (revenuesList.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7" style="text-align: center; padding: 40px;">لا توجد إيرادات مسجلة</td></tr>';
                return;
            }
            
            tableBody.innerHTML = revenuesList.map(revenue => `
                <tr>
                    <td>${formatDate(revenue.date)}</td>
                    <td>${revenue.source_type}</td>
                    <td class="currency">${formatCurrency(revenue.amount)}</td>
                    <td>${revenue.description || '-'}</td>
                    <td>${revenue.created_by_name || 'غير محدد'}</td>
                    <td>${formatDate(revenue.created_at)}</td>
                    <td>
                        <button class="btn btn-warning btn-sm" onclick="editRevenue(${revenue.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="deleteRevenue(${revenue.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // فلترة الإيرادات
        function filterRevenues() {
            const searchTerm = document.getElementById('searchRevenue').value.toLowerCase();
            const sourceFilter = document.getElementById('filterSource').value;
            const dateFrom = document.getElementById('filterDateFrom').value;
            const dateTo = document.getElementById('filterDateTo').value;
            
            const filteredRevenues = revenues.filter(revenue => {
                const matchesSearch = (revenue.description || '').toLowerCase().includes(searchTerm);
                const matchesSource = !sourceFilter || revenue.source_type === sourceFilter;
                const matchesDateFrom = !dateFrom || revenue.date >= dateFrom;
                const matchesDateTo = !dateTo || revenue.date <= dateTo;
                
                return matchesSearch && matchesSource && matchesDateFrom && matchesDateTo;
            });
            
            displayRevenues(filteredRevenues);
        }

        // إضافة مستمعين للبحث المباشر
        document.getElementById('searchRevenue').addEventListener('input', filterRevenues);
        document.getElementById('filterSource').addEventListener('change', filterRevenues);
        document.getElementById('filterDateFrom').addEventListener('change', filterRevenues);
        document.getElementById('filterDateTo').addEventListener('change', filterRevenues);

        // عرض نافذة إضافة إيراد
        function showAddRevenueModal() {
            currentRevenue = null;
            document.getElementById('revenueModalTitle').textContent = 'إضافة إيراد جديد';
            document.getElementById('revenueForm').reset();
            document.getElementById('revenueId').value = '';
            document.getElementById('revenueDate').value = new Date().toISOString().split('T')[0];
            document.getElementById('revenueModal').style.display = 'flex';
        }

        // تعديل إيراد
        function editRevenue(id) {
            currentRevenue = revenues.find(rev => rev.id == id);
            if (!currentRevenue) return;
            
            document.getElementById('revenueModalTitle').textContent = 'تعديل الإيراد';
            document.getElementById('revenueId').value = currentRevenue.id;
            document.getElementById('revenueDate').value = currentRevenue.date;
            document.getElementById('sourceType').value = currentRevenue.source_type;
            document.getElementById('revenueAmount').value = currentRevenue.amount;
            document.getElementById('revenueDescription').value = currentRevenue.description || '';
            
            document.getElementById('revenueModal').style.display = 'flex';
        }

        // حفظ الإيراد
        async function saveRevenue() {
            const form = document.getElementById('revenueForm');
            const formData = new FormData(form);
            
            const action = document.getElementById('revenueId').value ? 'update' : 'create';
            formData.append('action', action);
            
            try {
                const response = await fetch('revenues.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert(result.message, 'success');
                    closeRevenueModal();
                    loadRevenues();
                    loadStatistics();
                } else {
                    showAlert('خطأ: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('خطأ في حفظ الإيراد:', error);
                showAlert('حدث خطأ في حفظ البيانات', 'danger');
            }
        }

        // حذف إيراد
        async function deleteRevenue(id) {
            if (!confirm('هل أنت متأكد من حذف هذا الإيراد؟')) return;
            
            try {
                const formData = new FormData();
                formData.append('action', 'delete');
                formData.append('id', id);
                
                const response = await fetch('revenues.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert(result.message, 'success');
                    loadRevenues();
                    loadStatistics();
                } else {
                    showAlert('خطأ: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('خطأ في حذف الإيراد:', error);
                showAlert('حدث خطأ في حذف الإيراد', 'danger');
            }
        }

        // إغلاق النافذة المنبثقة
        function closeRevenueModal() {
            document.getElementById('revenueModal').style.display = 'none';
        }

        // تصدير البيانات
        function exportRevenues() {
            const headers = ['التاريخ', 'مصدر الإيراد', 'المبلغ', 'الوصف'];
            const csvContent = [
                headers.join(','),
                ...revenues.map(rev => [
                    rev.date,
                    rev.source_type,
                    rev.amount,
                    (rev.description || '').replace(/,/g, ';')
                ].join(','))
            ].join('\n');
            
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = 'revenues_' + new Date().toISOString().split('T')[0] + '.csv';
            link.click();
        }

        // عرض التقرير الشهري
        function showMonthlyReport() {
            const currentMonth = new Date().toISOString().slice(0, 7);
            const monthlyRevenues = revenues.filter(rev => rev.date.startsWith(currentMonth));
            
            const totalAmount = monthlyRevenues.reduce((sum, rev) => sum + parseFloat(rev.amount), 0);
            const sourceBreakdown = {};
            
            monthlyRevenues.forEach(rev => {
                sourceBreakdown[rev.source_type] = (sourceBreakdown[rev.source_type] || 0) + parseFloat(rev.amount);
            });
            
            let reportContent = `تقرير الإيرادات لشهر ${currentMonth}\n\n`;
            reportContent += `إجمالي الإيرادات: ${formatCurrency(totalAmount)} د.ج\n`;
            reportContent += `عدد العمليات: ${monthlyRevenues.length}\n\n`;
            reportContent += `التوزيع حسب المصدر:\n`;
            
            Object.entries(sourceBreakdown).forEach(([source, amount]) => {
                reportContent += `${source}: ${formatCurrency(amount)} د.ج\n`;
            });
            
            alert(reportContent);
        }

        // وظائف مساعدة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-DZ', {
                style: 'decimal',
                minimumFractionDigits: 2
            }).format(amount);
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-DZ');
        }

        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alert.style.position = 'fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.style.minWidth = '300px';
            
            document.body.appendChild(alert);
            
            setTimeout(() => {
                document.body.removeChild(alert);
            }, 5000);
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                window.location.href = 'auth.php?action=logout';
            }
        }
    </script>
</body>
</html>
