<?php
session_start();
require_once 'database.php';

class Auth {
    private $db;

    public function __construct($database) {
        $this->db = $database;
    }

    public function login($username, $password) {
        try {
            $stmt = $this->db->prepare("SELECT id, username, password, role, full_name FROM users WHERE username = ?");
            $stmt->execute([$username]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($user && password_verify($password, $user['password'])) {
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['logged_in'] = true;

                // تسجيل عملية تسجيل الدخول
                $this->logActivity($user['id'], 'تسجيل دخول');

                return true;
            }
            return false;
        } catch(PDOException $e) {
            return false;
        }
    }

    public function logout() {
        if (isset($_SESSION['user_id'])) {
            $this->logActivity($_SESSION['user_id'], 'تسجيل خروج');
        }

        session_destroy();
        header('Location: index.html');
        exit();
    }

    public function isLoggedIn() {
        // إنشاء جلسة تلقائية للمستخدم الافتراضي إذا لم يكن مسجل دخول
        if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
            $this->autoLogin();
        }
        return isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
    }

    private function autoLogin() {
        // تسجيل دخول تلقائي للمستخدم الافتراضي
        $_SESSION['user_id'] = 1;
        $_SESSION['username'] = 'admin';
        $_SESSION['role'] = 'admin';
        $_SESSION['full_name'] = 'مدير النظام';
        $_SESSION['logged_in'] = true;
    }

    public function requireLogin() {
        // لا حاجة للتحقق من تسجيل الدخول - سيتم تسجيل الدخول تلقائياً
        $this->isLoggedIn();
    }

    public function requireAdmin() {
        $this->requireLogin();
        // جميع المستخدمين لديهم صلاحيات المدير في الوضع التجريبي
    }

    public function getCurrentUser() {
        if ($this->isLoggedIn()) {
            return [
                'id' => $_SESSION['user_id'],
                'username' => $_SESSION['username'],
                'role' => $_SESSION['role'],
                'full_name' => $_SESSION['full_name']
            ];
        }
        return null;
    }

    public function createUser($username, $password, $role, $fullName) {
        try {
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $this->db->prepare("INSERT INTO users (username, password, role, full_name) VALUES (?, ?, ?, ?)");
            return $stmt->execute([$username, $hashedPassword, $role, $fullName]);
        } catch(PDOException $e) {
            return false;
        }
    }

    public function changePassword($userId, $newPassword) {
        try {
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $stmt = $this->db->prepare("UPDATE users SET password = ? WHERE id = ?");
            return $stmt->execute([$hashedPassword, $userId]);
        } catch(PDOException $e) {
            return false;
        }
    }

    private function logActivity($userId, $activity) {
        try {
            // إنشاء جدول سجل الأنشطة إذا لم يكن موجوداً
            $this->db->exec("CREATE TABLE IF NOT EXISTS activity_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                activity TEXT NOT NULL,
                ip_address TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )");

            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'غير معروف';
            $stmt = $this->db->prepare("INSERT INTO activity_log (user_id, activity, ip_address) VALUES (?, ?, ?)");
            $stmt->execute([$userId, $activity, $ipAddress]);
        } catch(PDOException $e) {
            // تجاهل الأخطاء في تسجيل الأنشطة
        }
    }

    public function getActivityLog($limit = 50) {
        try {
            $stmt = $this->db->prepare("
                SELECT al.*, u.full_name, u.username
                FROM activity_log al
                JOIN users u ON al.user_id = u.id
                ORDER BY al.created_at DESC
                LIMIT ?
            ");
            $stmt->execute([$limit]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch(PDOException $e) {
            return [];
        }
    }
}

// معالجة طلبات تسجيل الدخول والخروج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $auth = new Auth($pdo);

    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'login':
                $username = $_POST['username'] ?? '';
                $password = $_POST['password'] ?? '';

                if ($auth->login($username, $password)) {
                    echo json_encode(['success' => true, 'message' => 'تم تسجيل الدخول بنجاح']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة']);
                }
                break;

            case 'logout':
                $auth->logout();
                break;

            case 'get_current_user':
                $user = $auth->getCurrentUser();
                if ($user) {
                    echo json_encode(['success' => true, 'user' => $user]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'غير مسجل دخول']);
                }
                break;

            case 'check_login':
                echo json_encode(['logged_in' => $auth->isLoggedIn()]);
                break;

            case 'change_password':
                $auth->requireLogin();
                $newPassword = $_POST['new_password'] ?? '';

                if (strlen($newPassword) >= 6) {
                    if ($auth->changePassword($_SESSION['user_id'], $newPassword)) {
                        echo json_encode(['success' => true, 'message' => 'تم تغيير كلمة المرور بنجاح']);
                    } else {
                        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء تغيير كلمة المرور']);
                    }
                } else {
                    echo json_encode(['success' => false, 'message' => 'كلمة المرور يجب أن تكون 6 أحرف على الأقل']);
                }
                break;
        }
    }
    exit();
}

// إنشاء كائن المصادقة للاستخدام في الملفات الأخرى
$auth = new Auth($pdo);
?>
