<?php
require_once 'auth.php';

// التحقق من تسجيل الدخول
$auth->requireLogin();

header('Content-Type: application/json; charset=utf-8');

try {
    // إحصائيات عامة
    $stats = [];
    
    // عدد الموظفين
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM employees");
    $stats['totalEmployees'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // إجمالي الإيرادات
    $stmt = $pdo->query("SELECT COALESCE(SUM(amount), 0) as total FROM revenues");
    $stats['totalRevenues'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // إجمالي المصروفات
    $stmt = $pdo->query("SELECT COALESCE(SUM(amount), 0) as total FROM expenses");
    $stats['totalExpenses'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // السلفات النشطة
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM loans WHERE status IN ('موافق عليه', 'قيد الدراسة')");
    $stats['activeLoans'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // الإيرادات الشهرية (آخر 6 أشهر)
    $monthlyRevenues = [];
    for ($i = 5; $i >= 0; $i--) {
        $month = date('Y-m', strtotime("-$i months"));
        $stmt = $pdo->prepare("SELECT COALESCE(SUM(amount), 0) as total FROM revenues WHERE strftime('%Y-%m', date) = ?");
        $stmt->execute([$month]);
        $monthlyRevenues[] = floatval($stmt->fetch(PDO::FETCH_ASSOC)['total']);
    }
    $stats['monthlyRevenues'] = $monthlyRevenues;
    
    // المصروفات الشهرية (آخر 6 أشهر)
    $monthlyExpenses = [];
    for ($i = 5; $i >= 0; $i--) {
        $month = date('Y-m', strtotime("-$i months"));
        $stmt = $pdo->prepare("SELECT COALESCE(SUM(amount), 0) as total FROM expenses WHERE strftime('%Y-%m', date) = ?");
        $stmt->execute([$month]);
        $monthlyExpenses[] = floatval($stmt->fetch(PDO::FETCH_ASSOC)['total']);
    }
    $stats['monthlyExpenses'] = $monthlyExpenses;
    
    // توزيع المساعدات الاجتماعية
    $assistanceTypes = ['الوفاة', 'الزواج', 'الختان', 'المرض', 'الكوارث'];
    $assistanceDistribution = [];
    
    foreach ($assistanceTypes as $type) {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM expenses WHERE subcategory = ?");
        $stmt->execute([$type]);
        $assistanceDistribution[] = intval($stmt->fetch(PDO::FETCH_ASSOC)['count']);
    }
    $stats['assistanceDistribution'] = $assistanceDistribution;
    
    // الطلبات الحديثة
    $stmt = $pdo->prepare("
        SELECT 
            e.id,
            e.category as type,
            emp.full_name as employee_name,
            e.amount,
            e.date,
            e.status
        FROM expenses e
        LEFT JOIN employees emp ON e.employee_id = emp.id
        ORDER BY e.created_at DESC
        LIMIT 10
        
        UNION ALL
        
        SELECT 
            l.id,
            'سلفة مالية' as type,
            emp.full_name as employee_name,
            l.amount,
            l.start_date as date,
            l.status
        FROM loans l
        LEFT JOIN employees emp ON l.employee_id = emp.id
        ORDER BY l.created_at DESC
        LIMIT 5
        
        UNION ALL
        
        SELECT 
            u.id,
            'العمرة' as type,
            emp.full_name as employee_name,
            u.total_cost as amount,
            u.created_at as date,
            u.status
        FROM umrah u
        LEFT JOIN employees emp ON u.employee_id = emp.id
        ORDER BY u.created_at DESC
        LIMIT 5
    ");
    $stmt->execute();
    $recentRequests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // ترتيب الطلبات حسب التاريخ
    usort($recentRequests, function($a, $b) {
        return strtotime($b['date']) - strtotime($a['date']);
    });
    
    $stats['recentRequests'] = array_slice($recentRequests, 0, 10);
    
    // التنبيهات والإشعارات
    $notifications = [];
    
    // تحقق من السلفات المتأخرة
    $stmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM loan_installments li
        JOIN loans l ON li.loan_id = l.id
        WHERE li.status = 'غير مدفوع' 
        AND li.due_date < date('now')
        AND l.status = 'موافق عليه'
    ");
    $overdueLoans = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($overdueLoans > 0) {
        $notifications[] = [
            'type' => 'warning',
            'icon' => 'exclamation-triangle',
            'message' => "يوجد $overdueLoans قسط متأخر في السلفات المالية"
        ];
    }
    
    // تحقق من الطلبات المعلقة
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM expenses WHERE status = 'قيد الدراسة'");
    $pendingExpenses = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM loans WHERE status = 'قيد الدراسة'");
    $pendingLoans = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $totalPending = $pendingExpenses + $pendingLoans;
    
    if ($totalPending > 0) {
        $notifications[] = [
            'type' => 'info',
            'icon' => 'clock',
            'message' => "يوجد $totalPending طلب في انتظار المراجعة"
        ];
    }
    
    // تحقق من الرصيد المنخفض (مثال)
    $totalBalance = $stats['totalRevenues'] - $stats['totalExpenses'];
    if ($totalBalance < 100000) {
        $notifications[] = [
            'type' => 'danger',
            'icon' => 'exclamation-circle',
            'message' => 'تحذير: الرصيد المتاح منخفض'
        ];
    }
    
    $stats['notifications'] = $notifications;
    
    echo json_encode([
        'success' => true,
        'data' => $stats
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في تحميل البيانات: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
