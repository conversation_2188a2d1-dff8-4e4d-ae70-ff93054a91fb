<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - لجنة الخدمات الاجتماعية</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="dashboard">
    <!-- شريط التنقل العلوي -->
    <nav class="navbar">
        <div class="navbar-content">
            <a href="dashboard.html" class="navbar-brand">
                <i class="fas fa-users"></i>
                لجنة الخدمات الاجتماعية - ولاية الجلفة
            </a>
            <div class="navbar-user">
                <div class="user-info">
                    <i class="fas fa-user"></i>
                    <span id="userName">مرحباً</span>
                </div>
                <button class="btn btn-logout" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>
    </nav>

    <!-- الشريط الجانبي -->
    <aside class="sidebar">
        <ul class="sidebar-menu">
            <li><a href="dashboard.html" class="active"><i class="fas fa-tachometer-alt"></i>لوحة التحكم</a></li>
            <li><a href="employees.html"><i class="fas fa-users"></i>إدارة الموظفين</a></li>
            <li><a href="revenues.html"><i class="fas fa-arrow-up"></i>الإيرادات</a></li>
            <li><a href="expenses.html"><i class="fas fa-arrow-down"></i>المصروفات</a></li>
            <li><a href="loans.html"><i class="fas fa-money-bill-wave"></i>السلفات المالية</a></li>
            <li><a href="umrah.html"><i class="fas fa-kaaba"></i>العمرة</a></li>
            <li><a href="vacation.html"><i class="fas fa-umbrella-beach"></i>الاصطياف</a></li>
            <li><a href="reports.html"><i class="fas fa-chart-bar"></i>التقارير والإحصائيات</a></li>
            <li><a href="settings.html"><i class="fas fa-cog"></i>الإعدادات</a></li>
        </ul>
    </aside>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">لوحة التحكم الرئيسية</h1>
            <p class="page-subtitle">نظرة عامة على أنشطة لجنة الخدمات الاجتماعية</p>
        </div>

        <!-- بطاقات الإحصائيات -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon blue">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number" id="totalEmployees">0</div>
                <div class="stat-label">إجمالي الموظفين</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon green">
                    <i class="fas fa-arrow-up"></i>
                </div>
                <div class="stat-number currency" id="totalRevenues">0</div>
                <div class="stat-label">إجمالي الإيرادات</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon orange">
                    <i class="fas fa-arrow-down"></i>
                </div>
                <div class="stat-number currency" id="totalExpenses">0</div>
                <div class="stat-label">إجمالي المصروفات</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon purple">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-number" id="activeLoans">0</div>
                <div class="stat-label">السلفات النشطة</div>
            </div>
        </div>

        <!-- الرسوم البيانية والجداول -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
            <!-- رسم بياني للإيرادات والمصروفات -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">الإيرادات والمصروفات الشهرية</h3>
                </div>
                <div class="card-body">
                    <canvas id="revenueExpenseChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- رسم بياني للمساعدات -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">توزيع المساعدات الاجتماعية</h3>
                </div>
                <div class="card-body">
                    <canvas id="assistanceChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- الطلبات الحديثة -->
        <div class="table-container">
            <div class="table-header">
                <h3 class="table-title">الطلبات الحديثة</h3>
                <button class="btn btn-primary btn-sm" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث
                </button>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>نوع الطلب</th>
                        <th>اسم الموظف</th>
                        <th>المبلغ</th>
                        <th>التاريخ</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="recentRequestsTable">
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 40px;">
                            <div class="loading">
                                <div class="spinner"></div>
                                <p>جاري تحميل البيانات...</p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- التنبيهات -->
        <div class="card" style="margin-top: 30px;">
            <div class="card-header">
                <h3 class="card-title">التنبيهات والإشعارات</h3>
            </div>
            <div class="card-body" id="notificationsContainer">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    مرحباً بك في نظام إدارة لجنة الخدمات الاجتماعية لولاية الجلفة
                </div>
            </div>
        </div>
    </main>

    <script>
        // متغيرات عامة
        let currentUser = null;
        let dashboardData = {};

        // تحميل البيانات عند تحميل الصفحة
        window.addEventListener('load', function() {
            checkAuthentication();
            loadDashboardData();
        });

        // التحقق من المصادقة
        async function checkAuthentication() {
            try {
                const response = await fetch('auth.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=get_current_user'
                });
                
                const result = await response.json();
                
                if (result.success && result.user) {
                    currentUser = result.user;
                    document.getElementById('userName').textContent = `مرحباً، ${result.user.full_name}`;
                } else {
                    window.location.href = 'index.html';
                }
            } catch (error) {
                console.error('خطأ في التحقق من المصادقة:', error);
                window.location.href = 'index.html';
            }
        }

        // تحميل بيانات لوحة التحكم
        async function loadDashboardData() {
            try {
                const response = await fetch('dashboard_data.php');
                const data = await response.json();
                
                if (data.success) {
                    dashboardData = data.data;
                    updateStatistics();
                    updateCharts();
                    updateRecentRequests();
                    updateNotifications();
                }
            } catch (error) {
                console.error('خطأ في تحميل البيانات:', error);
            }
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            document.getElementById('totalEmployees').textContent = dashboardData.totalEmployees || 0;
            document.getElementById('totalRevenues').textContent = formatCurrency(dashboardData.totalRevenues || 0);
            document.getElementById('totalExpenses').textContent = formatCurrency(dashboardData.totalExpenses || 0);
            document.getElementById('activeLoans').textContent = dashboardData.activeLoans || 0;
        }

        // تحديث الرسوم البيانية
        function updateCharts() {
            // رسم بياني للإيرادات والمصروفات
            const ctx1 = document.getElementById('revenueExpenseChart').getContext('2d');
            new Chart(ctx1, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'الإيرادات',
                        data: dashboardData.monthlyRevenues || [0, 0, 0, 0, 0, 0],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'المصروفات',
                        data: dashboardData.monthlyExpenses || [0, 0, 0, 0, 0, 0],
                        borderColor: '#f093fb',
                        backgroundColor: 'rgba(240, 147, 251, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    }
                }
            });

            // رسم بياني للمساعدات
            const ctx2 = document.getElementById('assistanceChart').getContext('2d');
            new Chart(ctx2, {
                type: 'doughnut',
                data: {
                    labels: ['الوفاة', 'الزواج', 'الختان', 'المرض', 'الكوارث'],
                    datasets: [{
                        data: dashboardData.assistanceDistribution || [0, 0, 0, 0, 0],
                        backgroundColor: [
                            '#667eea',
                            '#56ab2f',
                            '#f093fb',
                            '#4facfe',
                            '#ff6b6b'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        }
                    }
                }
            });
        }

        // تحديث الطلبات الحديثة
        function updateRecentRequests() {
            const tableBody = document.getElementById('recentRequestsTable');
            const requests = dashboardData.recentRequests || [];
            
            if (requests.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 40px;">لا توجد طلبات حديثة</td></tr>';
                return;
            }
            
            tableBody.innerHTML = requests.map(request => `
                <tr>
                    <td>${request.type}</td>
                    <td>${request.employee_name}</td>
                    <td class="currency">${formatCurrency(request.amount)}</td>
                    <td>${formatDate(request.date)}</td>
                    <td><span class="status status-${getStatusClass(request.status)}">${request.status}</span></td>
                    <td>
                        <button class="btn btn-info btn-sm" onclick="viewRequest(${request.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // تحديث التنبيهات
        function updateNotifications() {
            const container = document.getElementById('notificationsContainer');
            const notifications = dashboardData.notifications || [];
            
            if (notifications.length === 0) {
                container.innerHTML = '<div class="alert alert-info"><i class="fas fa-info-circle"></i> لا توجد تنبيهات جديدة</div>';
                return;
            }
            
            container.innerHTML = notifications.map(notification => `
                <div class="alert alert-${notification.type}">
                    <i class="fas fa-${notification.icon}"></i>
                    ${notification.message}
                </div>
            `).join('');
        }

        // وظائف مساعدة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-DZ', {
                style: 'decimal',
                minimumFractionDigits: 2
            }).format(amount);
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-DZ');
        }

        function getStatusClass(status) {
            switch (status) {
                case 'موافق عليه': return 'approved';
                case 'مرفوض': return 'rejected';
                default: return 'pending';
            }
        }

        function viewRequest(id) {
            // فتح نافذة تفاصيل الطلب
            alert(`عرض تفاصيل الطلب رقم: ${id}`);
        }

        function refreshData() {
            loadDashboardData();
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                window.location.href = 'auth.php?action=logout';
            }
        }
    </script>
</body>
</html>
