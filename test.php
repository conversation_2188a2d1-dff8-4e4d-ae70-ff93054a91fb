<?php
echo "PHP is working correctly!<br>";
echo "PHP Version: " . phpversion() . "<br>";
echo "SQLite Support: " . (extension_loaded('sqlite3') ? 'Yes' : 'No') . "<br>";
echo "PDO Support: " . (extension_loaded('pdo') ? 'Yes' : 'No') . "<br>";
echo "Current Time: " . date('Y-m-d H:i:s') . "<br>";

// Test database connection
try {
    $pdo = new PDO('sqlite:social_services.db');
    echo "Database Connection: Success<br>";
    
    // Check if tables exist
    $tables = ['users', 'employees', 'revenues', 'expenses', 'loans', 'umrah', 'vacation'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='$table'");
        $exists = $stmt->fetch() ? 'Yes' : 'No';
        echo "Table '$table': $exists<br>";
    }
    
} catch (Exception $e) {
    echo "Database Error: " . $e->getMessage() . "<br>";
}
?>
