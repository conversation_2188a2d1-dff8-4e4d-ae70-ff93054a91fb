<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المصروفات - لجنة الخدمات الاجتماعية</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="dashboard">
    <!-- شريط التنقل العلوي -->
    <nav class="navbar">
        <div class="navbar-content">
            <a href="dashboard.html" class="navbar-brand">
                <i class="fas fa-users"></i>
                لجنة الخدمات الاجتماعية - ولاية الجلفة
            </a>
            <div class="navbar-user">
                <div class="user-info">
                    <i class="fas fa-user"></i>
                    <span id="userName">مرحباً، مدير النظام</span>
                </div>
                <button class="btn btn-logout" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>
    </nav>

    <!-- الشريط الجانبي -->
    <aside class="sidebar">
        <ul class="sidebar-menu">
            <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i>لوحة التحكم</a></li>
            <li><a href="employees.html"><i class="fas fa-users"></i>إدارة الموظفين</a></li>
            <li><a href="revenues.html"><i class="fas fa-arrow-up"></i>الإيرادات</a></li>
            <li><a href="expenses.html" class="active"><i class="fas fa-arrow-down"></i>المصروفات</a></li>
            <li><a href="loans.html"><i class="fas fa-money-bill-wave"></i>السلفات المالية</a></li>
            <li><a href="umrah.html"><i class="fas fa-kaaba"></i>العمرة</a></li>
            <li><a href="vacation.html"><i class="fas fa-umbrella-beach"></i>الاصطياف</a></li>
            <li><a href="reports.html"><i class="fas fa-chart-bar"></i>التقارير والإحصائيات</a></li>
            <li><a href="settings.html"><i class="fas fa-cog"></i>الإعدادات</a></li>
        </ul>
    </aside>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">إدارة المصروفات</h1>
            <p class="page-subtitle">إدارة المساعدات الاجتماعية والمصروفات</p>
        </div>

        <!-- إحصائيات المصروفات -->
        <div class="stats-grid" style="margin-bottom: 30px;">
            <div class="stat-card">
                <div class="stat-icon orange">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-number currency" id="totalExpenses">0</div>
                <div class="stat-label">إجمالي المصروفات</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon blue">
                    <i class="fas fa-heart"></i>
                </div>
                <div class="stat-number currency" id="socialAssistance">0</div>
                <div class="stat-label">المساعدات الاجتماعية</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon green">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="stat-number currency" id="scholarships">0</div>
                <div class="stat-label">المنح الدراسية</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon purple">
                    <i class="fas fa-stethoscope"></i>
                </div>
                <div class="stat-number currency" id="healthExpenses">0</div>
                <div class="stat-label">المصروفات الصحية</div>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div style="margin-bottom: 20px;">
            <button class="btn btn-primary" onclick="showAddExpenseModal()">
                <i class="fas fa-plus"></i>
                إضافة مصروف جديد
            </button>
            <button class="btn btn-success" onclick="exportExpenses()">
                <i class="fas fa-download"></i>
                تصدير البيانات
            </button>
        </div>

        <!-- البحث والفلترة -->
        <div class="card" style="margin-bottom: 20px;">
            <div class="card-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="searchExpense">البحث</label>
                        <input type="text" id="searchExpense" class="form-control" placeholder="البحث بالموظف أو الوصف">
                    </div>
                    <div class="form-group">
                        <label for="filterCategory">الفئة</label>
                        <select id="filterCategory" class="form-control">
                            <option value="">جميع الفئات</option>
                            <option value="المساعدات الاجتماعية">المساعدات الاجتماعية</option>
                            <option value="المنح الدراسية">المنح الدراسية</option>
                            <option value="الرحلات والتخييم">الرحلات والتخييم</option>
                            <option value="الصحة">الصحة</option>
                            <option value="السكن">السكن</option>
                            <option value="المتقاعدون">المتقاعدون</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="filterStatus">الحالة</label>
                        <select id="filterStatus" class="form-control">
                            <option value="">جميع الحالات</option>
                            <option value="قيد الدراسة">قيد الدراسة</option>
                            <option value="موافق عليه">موافق عليه</option>
                            <option value="مرفوض">مرفوض</option>
                        </select>
                    </div>
                    <div class="form-group" style="display: flex; align-items: end;">
                        <button class="btn btn-info" onclick="filterExpenses()">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول المصروفات -->
        <div class="table-container">
            <div class="table-header">
                <h3 class="table-title">قائمة المصروفات</h3>
                <button class="btn btn-primary btn-sm" onclick="loadExpenses()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث
                </button>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>الفئة</th>
                        <th>النوع</th>
                        <th>الموظف</th>
                        <th>المبلغ</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="expensesTable">
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 40px;">
                            <div class="loading">
                                <div class="spinner"></div>
                                <p>جاري تحميل البيانات...</p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </main>

    <!-- نافذة إضافة/تعديل مصروف -->
    <div id="expenseModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="expenseModalTitle">إضافة مصروف جديد</h3>
                <button class="btn-close" onclick="closeExpenseModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="expenseForm">
                    <input type="hidden" id="expenseId" name="id">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="expenseDate">التاريخ *</label>
                            <input type="date" id="expenseDate" name="date" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="expenseCategory">الفئة *</label>
                            <select id="expenseCategory" name="category" class="form-control" required onchange="updateSubcategories()">
                                <option value="">اختر الفئة</option>
                                <option value="المساعدات الاجتماعية">المساعدات الاجتماعية</option>
                                <option value="المنح الدراسية">المنح الدراسية</option>
                                <option value="الرحلات والتخييم">الرحلات والتخييم</option>
                                <option value="الصحة">الصحة</option>
                                <option value="السكن">السكن</option>
                                <option value="المتقاعدون">المتقاعدون</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="expenseSubcategory">النوع *</label>
                            <select id="expenseSubcategory" name="subcategory" class="form-control" required>
                                <option value="">اختر النوع</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="expenseEmployee">الموظف</label>
                            <select id="expenseEmployee" name="employee_id" class="form-control">
                                <option value="">اختر الموظف</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="expenseAmount">المبلغ (د.ج) *</label>
                        <input type="number" id="expenseAmount" name="amount" class="form-control" step="0.01" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="expenseDescription">الوصف</label>
                        <textarea id="expenseDescription" name="description" class="form-control" rows="3"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="expenseAttachments">المرفقات</label>
                        <input type="file" id="expenseAttachments" name="attachments" class="form-control" multiple accept=".pdf,.jpg,.jpeg,.png">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeExpenseModal()">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveExpense()">حفظ</button>
            </div>
        </div>
    </div>

    <script>
        let expenses = [];
        let employees = [];
        
        // تعريف الفئات الفرعية
        const subcategories = {
            'المساعدات الاجتماعية': ['الوفاة', 'الزواج', 'الختان', 'الكوارث', 'المرض'],
            'المنح الدراسية': ['منحة التفوق', 'منحة الحاجة', 'منحة الجامعة'],
            'الرحلات والتخييم': ['رحلة صيفية', 'تخييم', 'رحلة ثقافية'],
            'الصحة': ['استرجاع تكاليف طبية', 'عمليات جراحية', 'علاج مزمن'],
            'السكن': ['مساعدة سكن', 'إصلاحات', 'إيجار'],
            'المتقاعدون': ['مساعدة استثنائية', 'رعاية صحية', 'مساعدة عائلية']
        };

        // تحميل البيانات عند تحميل الصفحة
        window.addEventListener('load', function() {
            loadEmployees();
            loadExpenses();
            loadStatistics();
            
            // تعيين التاريخ الحالي كافتراضي
            document.getElementById('expenseDate').value = new Date().toISOString().split('T')[0];
        });

        // تحميل قائمة الموظفين
        async function loadEmployees() {
            try {
                const response = await fetch('employees.php?action=list');
                const result = await response.json();
                
                if (result.success) {
                    employees = result.data;
                    populateEmployeeSelect();
                }
            } catch (error) {
                console.error('خطأ في تحميل الموظفين:', error);
            }
        }

        // ملء قائمة الموظفين
        function populateEmployeeSelect() {
            const select = document.getElementById('expenseEmployee');
            select.innerHTML = '<option value="">اختر الموظف</option>';
            
            employees.forEach(employee => {
                const option = document.createElement('option');
                option.value = employee.id;
                option.textContent = `${employee.full_name} - ${employee.registration_number}`;
                select.appendChild(option);
            });
        }

        // تحديث الفئات الفرعية
        function updateSubcategories() {
            const category = document.getElementById('expenseCategory').value;
            const subcategorySelect = document.getElementById('expenseSubcategory');
            
            subcategorySelect.innerHTML = '<option value="">اختر النوع</option>';
            
            if (category && subcategories[category]) {
                subcategories[category].forEach(sub => {
                    const option = document.createElement('option');
                    option.value = sub;
                    option.textContent = sub;
                    subcategorySelect.appendChild(option);
                });
            }
        }

        // تحميل قائمة المصروفات
        async function loadExpenses() {
            try {
                const response = await fetch('expenses.php?action=list');
                const result = await response.json();
                
                if (result.success) {
                    expenses = result.data;
                    displayExpenses(expenses);
                } else {
                    showAlert('خطأ في تحميل البيانات: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('خطأ في تحميل المصروفات:', error);
                showAlert('حدث خطأ في تحميل البيانات', 'danger');
            }
        }

        // تحميل الإحصائيات
        async function loadStatistics() {
            try {
                const response = await fetch('expenses.php?action=statistics');
                const result = await response.json();
                
                if (result.success) {
                    const stats = result.data;
                    document.getElementById('totalExpenses').textContent = formatCurrency(stats.total || 0);
                    document.getElementById('socialAssistance').textContent = formatCurrency(stats.social_assistance || 0);
                    document.getElementById('scholarships').textContent = formatCurrency(stats.scholarships || 0);
                    document.getElementById('healthExpenses').textContent = formatCurrency(stats.health || 0);
                }
            } catch (error) {
                console.error('خطأ في تحميل الإحصائيات:', error);
            }
        }

        // عرض المصروفات في الجدول
        function displayExpenses(expensesList) {
            const tableBody = document.getElementById('expensesTable');
            
            if (expensesList.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7" style="text-align: center; padding: 40px;">لا توجد مصروفات مسجلة</td></tr>';
                return;
            }
            
            tableBody.innerHTML = expensesList.map(expense => `
                <tr>
                    <td>${formatDate(expense.date)}</td>
                    <td>${expense.category}</td>
                    <td>${expense.subcategory || '-'}</td>
                    <td>${expense.employee_name || '-'}</td>
                    <td class="currency">${formatCurrency(expense.amount)}</td>
                    <td><span class="status status-${getStatusClass(expense.status)}">${expense.status}</span></td>
                    <td>
                        <button class="btn btn-info btn-sm" onclick="viewExpense(${expense.id})" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-warning btn-sm" onclick="editExpense(${expense.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-success btn-sm" onclick="approveExpense(${expense.id})" title="موافقة">
                            <i class="fas fa-check"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // فلترة المصروفات
        function filterExpenses() {
            const searchTerm = document.getElementById('searchExpense').value.toLowerCase();
            const categoryFilter = document.getElementById('filterCategory').value;
            const statusFilter = document.getElementById('filterStatus').value;
            
            const filteredExpenses = expenses.filter(expense => {
                const matchesSearch = (expense.employee_name || '').toLowerCase().includes(searchTerm) || 
                                    (expense.description || '').toLowerCase().includes(searchTerm);
                const matchesCategory = !categoryFilter || expense.category === categoryFilter;
                const matchesStatus = !statusFilter || expense.status === statusFilter;
                
                return matchesSearch && matchesCategory && matchesStatus;
            });
            
            displayExpenses(filteredExpenses);
        }

        // إضافة مستمعين للبحث المباشر
        document.getElementById('searchExpense').addEventListener('input', filterExpenses);
        document.getElementById('filterCategory').addEventListener('change', filterExpenses);
        document.getElementById('filterStatus').addEventListener('change', filterExpenses);

        // عرض نافذة إضافة مصروف
        function showAddExpenseModal() {
            document.getElementById('expenseModalTitle').textContent = 'إضافة مصروف جديد';
            document.getElementById('expenseForm').reset();
            document.getElementById('expenseId').value = '';
            document.getElementById('expenseDate').value = new Date().toISOString().split('T')[0];
            updateSubcategories();
            document.getElementById('expenseModal').style.display = 'flex';
        }

        // حفظ المصروف
        async function saveExpense() {
            const form = document.getElementById('expenseForm');
            const formData = new FormData(form);
            
            const action = document.getElementById('expenseId').value ? 'update' : 'create';
            formData.append('action', action);
            
            try {
                const response = await fetch('expenses.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert(result.message, 'success');
                    closeExpenseModal();
                    loadExpenses();
                    loadStatistics();
                } else {
                    showAlert('خطأ: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('خطأ في حفظ المصروف:', error);
                showAlert('حدث خطأ في حفظ البيانات', 'danger');
            }
        }

        // إغلاق النافذة المنبثقة
        function closeExpenseModal() {
            document.getElementById('expenseModal').style.display = 'none';
        }

        // وظائف مساعدة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-DZ', {
                style: 'decimal',
                minimumFractionDigits: 2
            }).format(amount);
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-DZ');
        }

        function getStatusClass(status) {
            switch (status) {
                case 'موافق عليه': return 'approved';
                case 'مرفوض': return 'rejected';
                default: return 'pending';
            }
        }

        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alert.style.position = 'fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.style.minWidth = '300px';
            
            document.body.appendChild(alert);
            
            setTimeout(() => {
                document.body.removeChild(alert);
            }, 5000);
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                window.location.href = 'index.html';
            }
        }
    </script>
</body>
</html>
