<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .test-item {
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .test-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .test-error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: #333;">اختبار نظام إدارة لجنة الخدمات الاجتماعية</h1>
        
        <div class="test-item test-success">
            <span>✅ ملف CSS</span>
            <span>يعمل بشكل صحيح</span>
        </div>
        
        <div class="test-item" id="phpTest">
            <span>🔄 اختبار PHP</span>
            <button class="test-button" onclick="testPHP()">اختبار</button>
        </div>
        
        <div class="test-item" id="databaseTest">
            <span>🔄 اختبار قاعدة البيانات</span>
            <button class="test-button" onclick="testDatabase()">اختبار</button>
        </div>
        
        <div class="test-item" id="authTest">
            <span>🔄 اختبار نظام المصادقة</span>
            <button class="test-button" onclick="testAuth()">اختبار</button>
        </div>
        
        <div class="test-item" id="employeesTest">
            <span>🔄 اختبار API الموظفين</span>
            <button class="test-button" onclick="testEmployees()">اختبار</button>
        </div>
        
        <div class="test-item" id="revenuesTest">
            <span>🔄 اختبار API الإيرادات</span>
            <button class="test-button" onclick="testRevenues()">اختبار</button>
        </div>
        
        <div class="test-item" id="expensesTest">
            <span>🔄 اختبار API المصروفات</span>
            <button class="test-button" onclick="testExpenses()">اختبار</button>
        </div>
        
        <div class="test-item" id="loansTest">
            <span>🔄 اختبار API السلفات</span>
            <button class="test-button" onclick="testLoans()">اختبار</button>
        </div>
        
        <div class="test-item" id="umrahTest">
            <span>🔄 اختبار API العمرة</span>
            <button class="test-button" onclick="testUmrah()">اختبار</button>
        </div>
        
        <div class="test-item" id="vacationTest">
            <span>🔄 اختبار API الاصطياف</span>
            <button class="test-button" onclick="testVacation()">اختبار</button>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button class="test-button" onclick="testAll()" style="background: #28a745; font-size: 16px; padding: 15px 30px;">
                اختبار جميع المكونات
            </button>
        </div>
        
        <div style="text-align: center; margin-top: 20px;">
            <a href="index.html" class="test-button" style="text-decoration: none; background: #6c757d;">
                الانتقال للنظام الرئيسي
            </a>
        </div>
    </div>

    <script>
        async function testAPI(url, testId, name) {
            const element = document.getElementById(testId);
            element.innerHTML = `<span>🔄 اختبار ${name}</span><span>جاري الاختبار...</span>`;
            
            try {
                const response = await fetch(url);
                const data = await response.json();
                
                if (data.success || response.ok) {
                    element.className = 'test-item test-success';
                    element.innerHTML = `<span>✅ ${name}</span><span>يعمل بشكل صحيح</span>`;
                } else {
                    element.className = 'test-item test-error';
                    element.innerHTML = `<span>❌ ${name}</span><span>خطأ: ${data.message || 'غير محدد'}</span>`;
                }
            } catch (error) {
                element.className = 'test-item test-error';
                element.innerHTML = `<span>❌ ${name}</span><span>خطأ: ${error.message}</span>`;
            }
        }
        
        function testPHP() {
            testAPI('test.php', 'phpTest', 'PHP');
        }
        
        function testDatabase() {
            testAPI('database.php', 'databaseTest', 'قاعدة البيانات');
        }
        
        function testAuth() {
            testAPI('auth.php?action=check_login', 'authTest', 'نظام المصادقة');
        }
        
        function testEmployees() {
            testAPI('employees.php?action=list', 'employeesTest', 'API الموظفين');
        }
        
        function testRevenues() {
            testAPI('revenues.php?action=list', 'revenuesTest', 'API الإيرادات');
        }
        
        function testExpenses() {
            testAPI('expenses.php?action=list', 'expensesTest', 'API المصروفات');
        }
        
        function testLoans() {
            testAPI('loans.php?action=list', 'loansTest', 'API السلفات');
        }
        
        function testUmrah() {
            testAPI('umrah.php?action=list', 'umrahTest', 'API العمرة');
        }
        
        function testVacation() {
            testAPI('vacation.php?action=list', 'vacationTest', 'API الاصطياف');
        }
        
        async function testAll() {
            const tests = [
                { func: testPHP, delay: 500 },
                { func: testDatabase, delay: 1000 },
                { func: testAuth, delay: 1500 },
                { func: testEmployees, delay: 2000 },
                { func: testRevenues, delay: 2500 },
                { func: testExpenses, delay: 3000 },
                { func: testLoans, delay: 3500 },
                { func: testUmrah, delay: 4000 },
                { func: testVacation, delay: 4500 }
            ];
            
            tests.forEach(test => {
                setTimeout(test.func, test.delay);
            });
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(testAll, 1000);
        });
    </script>
</body>
</html>
