<?php
require_once 'auth.php';

// التحقق من تسجيل الدخول
$auth->requireLogin();

header('Content-Type: application/json; charset=utf-8');

class LoanManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    public function getAllLoans() {
        try {
            $stmt = $this->db->query("
                SELECT l.*, e.full_name as employee_name, e.registration_number
                FROM loans l
                LEFT JOIN employees e ON l.employee_id = e.id
                ORDER BY l.created_at DESC
            ");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع بيانات السلفات: " . $e->getMessage());
        }
    }
    
    public function getLoanById($id) {
        try {
            $stmt = $this->db->prepare("
                SELECT l.*, e.full_name as employee_name, e.registration_number
                FROM loans l
                LEFT JOIN employees e ON l.employee_id = e.id
                WHERE l.id = ?
            ");
            $stmt->execute([$id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع بيانات السلفة: " . $e->getMessage());
        }
    }
    
    public function createLoan($data) {
        try {
            $this->db->beginTransaction();
            
            // التحقق من وجود سلفة نشطة للموظف
            $stmt = $this->db->prepare("
                SELECT COUNT(*) FROM loans 
                WHERE employee_id = ? AND status IN ('قيد الدراسة', 'موافق عليه')
            ");
            $stmt->execute([$data['employee_id']]);
            
            if ($stmt->fetchColumn() > 0) {
                throw new Exception("يوجد سلفة نشطة للموظف. لا يمكن إضافة سلفة جديدة");
            }
            
            // التحقق من نسبة السلفة إلى الراتب (لا تتجاوز 50%)
            $maxLoanAmount = $data['monthly_salary'] * 0.5 * $data['installments_count'];
            if ($data['amount'] > $maxLoanAmount) {
                throw new Exception("مبلغ السلفة يتجاوز الحد المسموح (50% من الراتب)");
            }
            
            $stmt = $this->db->prepare("
                INSERT INTO loans (
                    employee_id, amount, loan_type, reason, monthly_salary,
                    installments_count, monthly_installment, start_date, attachments
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $result = $stmt->execute([
                $data['employee_id'],
                $data['amount'],
                $data['loan_type'],
                $data['reason'],
                $data['monthly_salary'],
                $data['installments_count'],
                $data['monthly_installment'],
                $data['start_date'],
                $data['attachments'] ?? ''
            ]);
            
            if (!$result) {
                throw new Exception("فشل في إضافة السلفة");
            }
            
            $loanId = $this->db->lastInsertId();
            
            // إنشاء جدول الأقساط
            $this->createInstallments($loanId, $data);
            
            $this->db->commit();
            return $loanId;
            
        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }
    
    private function createInstallments($loanId, $data) {
        $startDate = new DateTime($data['start_date']);
        $installmentAmount = $data['monthly_installment'];
        
        for ($i = 1; $i <= $data['installments_count']; $i++) {
            $dueDate = clone $startDate;
            $dueDate->add(new DateInterval("P" . ($i - 1) . "M"));
            
            $stmt = $this->db->prepare("
                INSERT INTO loan_installments (
                    loan_id, installment_number, amount, due_date
                ) VALUES (?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $loanId,
                $i,
                $installmentAmount,
                $dueDate->format('Y-m-d')
            ]);
        }
    }
    
    public function updateLoan($id, $data) {
        try {
            $stmt = $this->db->prepare("
                UPDATE loans SET 
                    employee_id = ?, amount = ?, loan_type = ?, reason = ?,
                    monthly_salary = ?, installments_count = ?, monthly_installment = ?,
                    start_date = ?, attachments = ?
                WHERE id = ?
            ");
            
            $result = $stmt->execute([
                $data['employee_id'],
                $data['amount'],
                $data['loan_type'],
                $data['reason'],
                $data['monthly_salary'],
                $data['installments_count'],
                $data['monthly_installment'],
                $data['start_date'],
                $data['attachments'] ?? '',
                $id
            ]);
            
            if (!$result) {
                throw new Exception("فشل في تحديث السلفة");
            }
            
            return true;
        } catch (PDOException $e) {
            throw new Exception("خطأ في تحديث السلفة: " . $e->getMessage());
        }
    }
    
    public function updateLoanStatus($id, $status) {
        try {
            $stmt = $this->db->prepare("UPDATE loans SET status = ? WHERE id = ?");
            $result = $stmt->execute([$status, $id]);
            
            if (!$result) {
                throw new Exception("فشل في تحديث حالة السلفة");
            }
            
            return true;
        } catch (PDOException $e) {
            throw new Exception("خطأ في تحديث حالة السلفة: " . $e->getMessage());
        }
    }
    
    public function getLoanInstallments($loanId) {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM loan_installments 
                WHERE loan_id = ? 
                ORDER BY installment_number ASC
            ");
            $stmt->execute([$loanId]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع الأقساط: " . $e->getMessage());
        }
    }
    
    public function payInstallment($installmentId, $paymentDate = null) {
        try {
            $paymentDate = $paymentDate ?: date('Y-m-d');
            
            $stmt = $this->db->prepare("
                UPDATE loan_installments 
                SET paid_date = ?, status = 'مدفوع' 
                WHERE id = ?
            ");
            
            $result = $stmt->execute([$paymentDate, $installmentId]);
            
            if (!$result) {
                throw new Exception("فشل في تسجيل دفع القسط");
            }
            
            // التحقق من اكتمال جميع الأقساط
            $stmt = $this->db->prepare("
                SELECT loan_id FROM loan_installments 
                WHERE id = ?
            ");
            $stmt->execute([$installmentId]);
            $loanId = $stmt->fetchColumn();
            
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as total, 
                       SUM(CASE WHEN status = 'مدفوع' THEN 1 ELSE 0 END) as paid
                FROM loan_installments 
                WHERE loan_id = ?
            ");
            $stmt->execute([$loanId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result['total'] == $result['paid']) {
                $this->updateLoanStatus($loanId, 'مكتمل');
            }
            
            return true;
        } catch (PDOException $e) {
            throw new Exception("خطأ في تسجيل دفع القسط: " . $e->getMessage());
        }
    }
    
    public function getLoanStatistics() {
        try {
            $stats = [];
            
            // إجمالي السلفات
            $stmt = $this->db->query("SELECT COUNT(*) as total FROM loans");
            $stats['total'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // السلفات المعتمدة
            $stmt = $this->db->query("SELECT COUNT(*) as approved FROM loans WHERE status = 'موافق عليه'");
            $stats['approved'] = $stmt->fetch(PDO::FETCH_ASSOC)['approved'];
            
            // السلفات قيد الدراسة
            $stmt = $this->db->query("SELECT COUNT(*) as pending FROM loans WHERE status = 'قيد الدراسة'");
            $stats['pending'] = $stmt->fetch(PDO::FETCH_ASSOC)['pending'];
            
            // إجمالي المبالغ
            $stmt = $this->db->query("SELECT COALESCE(SUM(amount), 0) as total_amount FROM loans WHERE status = 'موافق عليه'");
            $stats['total_amount'] = $stmt->fetch(PDO::FETCH_ASSOC)['total_amount'];
            
            // الأقساط المتأخرة
            $stmt = $this->db->query("
                SELECT COUNT(*) as overdue 
                FROM loan_installments li
                JOIN loans l ON li.loan_id = l.id
                WHERE li.status = 'غير مدفوع' 
                AND li.due_date < date('now')
                AND l.status = 'موافق عليه'
            ");
            $stats['overdue'] = $stmt->fetch(PDO::FETCH_ASSOC)['overdue'];
            
            return $stats;
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع الإحصائيات: " . $e->getMessage());
        }
    }
    
    public function getOverdueInstallments() {
        try {
            $stmt = $this->db->query("
                SELECT li.*, l.amount as loan_amount, e.full_name as employee_name
                FROM loan_installments li
                JOIN loans l ON li.loan_id = l.id
                JOIN employees e ON l.employee_id = e.id
                WHERE li.status = 'غير مدفوع' 
                AND li.due_date < date('now')
                AND l.status = 'موافق عليه'
                ORDER BY li.due_date ASC
            ");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع الأقساط المتأخرة: " . $e->getMessage());
        }
    }
}

// معالجة الطلبات
try {
    $loanManager = new LoanManager($pdo);
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    
    switch ($action) {
        case 'list':
            $loans = $loanManager->getAllLoans();
            echo json_encode([
                'success' => true,
                'data' => $loans
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'get':
            $id = $_GET['id'] ?? 0;
            if (!$id) {
                throw new Exception("معرف السلفة مطلوب");
            }
            
            $loan = $loanManager->getLoanById($id);
            if (!$loan) {
                throw new Exception("السلفة غير موجودة");
            }
            
            echo json_encode([
                'success' => true,
                'data' => $loan
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'create':
            $requiredFields = ['employee_id', 'amount', 'loan_type', 'reason', 'monthly_salary', 'installments_count', 'monthly_installment', 'start_date'];
            
            foreach ($requiredFields as $field) {
                if (empty($_POST[$field])) {
                    throw new Exception("الحقل $field مطلوب");
                }
            }
            
            $loanId = $loanManager->createLoan($_POST);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم إضافة طلب السلفة بنجاح',
                'loan_id' => $loanId
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'update':
            $id = $_POST['id'] ?? 0;
            if (!$id) {
                throw new Exception("معرف السلفة مطلوب");
            }
            
            $loanManager->updateLoan($id, $_POST);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث السلفة بنجاح'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'update_status':
            $id = $_POST['id'] ?? 0;
            $status = $_POST['status'] ?? '';
            
            if (!$id || !$status) {
                throw new Exception("معرف السلفة والحالة مطلوبان");
            }
            
            $loanManager->updateLoanStatus($id, $status);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث حالة السلفة بنجاح'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'installments':
            $loanId = $_GET['loan_id'] ?? 0;
            if (!$loanId) {
                throw new Exception("معرف السلفة مطلوب");
            }
            
            $installments = $loanManager->getLoanInstallments($loanId);
            
            echo json_encode([
                'success' => true,
                'data' => $installments
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'pay_installment':
            $installmentId = $_POST['installment_id'] ?? 0;
            $paymentDate = $_POST['payment_date'] ?? null;
            
            if (!$installmentId) {
                throw new Exception("معرف القسط مطلوب");
            }
            
            $loanManager->payInstallment($installmentId, $paymentDate);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم تسجيل دفع القسط بنجاح'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'statistics':
            $stats = $loanManager->getLoanStatistics();
            
            echo json_encode([
                'success' => true,
                'data' => $stats
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'overdue':
            $overdueInstallments = $loanManager->getOverdueInstallments();
            
            echo json_encode([
                'success' => true,
                'data' => $overdueInstallments
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            throw new Exception("إجراء غير صحيح");
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
