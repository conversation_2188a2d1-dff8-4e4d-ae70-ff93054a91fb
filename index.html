<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة لجنة الخدمات الاجتماعية - ولاية الجلفة</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <i class="fas fa-users fa-3x" style="color: #667eea; margin-bottom: 20px;"></i>
                <h1>لجنة الخدمات الاجتماعية</h1>
                <p>ولاية الجلفة - مقر الولاية والدوائر</p>
            </div>

            <form id="loginForm">
                <div class="form-group">
                    <label for="username">اسم المستخدم</label>
                    <input type="text" id="username" name="username" class="form-control" required>
                </div>

                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <input type="password" id="password" name="password" class="form-control" required>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
            </form>

            <div id="loginMessage" class="alert" style="display: none; margin-top: 20px;"></div>

            <div class="loading" id="loginLoading">
                <div class="spinner"></div>
                <p>جاري تسجيل الدخول...</p>
            </div>

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e1e5e9; font-size: 14px; color: #666;">
                <p><strong>بيانات تجريبية:</strong></p>
                <p>المستخدم: admin</p>
                <p>كلمة المرور: admin123</p>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const messageDiv = document.getElementById('loginMessage');
            const loadingDiv = document.getElementById('loginLoading');

            // إخفاء الرسائل السابقة
            messageDiv.style.display = 'none';
            loadingDiv.style.display = 'block';

            try {
                const formData = new FormData();
                formData.append('action', 'login');
                formData.append('username', username);
                formData.append('password', password);

                const response = await fetch('auth.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                loadingDiv.style.display = 'none';

                if (result.success) {
                    messageDiv.className = 'alert alert-success';
                    messageDiv.textContent = result.message;
                    messageDiv.style.display = 'block';

                    // إعادة توجيه إلى لوحة التحكم
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 1000);
                } else {
                    messageDiv.className = 'alert alert-danger';
                    messageDiv.textContent = result.message;
                    messageDiv.style.display = 'block';
                }
            } catch (error) {
                loadingDiv.style.display = 'none';
                messageDiv.className = 'alert alert-danger';
                messageDiv.textContent = 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.';
                messageDiv.style.display = 'block';
            }
        });

        // التوجه مباشرة إلى لوحة التحكم
        window.addEventListener('load', function() {
            // في الوضع التجريبي، التوجه مباشرة إلى لوحة التحكم
            window.location.href = 'dashboard.html';
        });
    </script>
</body>
</html>
