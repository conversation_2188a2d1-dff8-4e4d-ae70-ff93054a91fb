<?php
require_once 'auth.php';

// التحقق من تسجيل الدخول
$auth->requireLogin();

header('Content-Type: application/json; charset=utf-8');

class EmployeeManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    public function getAllEmployees() {
        try {
            $stmt = $this->db->query("
                SELECT * FROM employees 
                ORDER BY full_name ASC
            ");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع بيانات الموظفين: " . $e->getMessage());
        }
    }
    
    public function getEmployeeById($id) {
        try {
            $stmt = $this->db->prepare("SELECT * FROM employees WHERE id = ?");
            $stmt->execute([$id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع بيانات الموظف: " . $e->getMessage());
        }
    }
    
    public function createEmployee($data) {
        try {
            // التحقق من عدم تكرار رقم التسجيل
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM employees WHERE registration_number = ?");
            $stmt->execute([$data['registration_number']]);
            
            if ($stmt->fetchColumn() > 0) {
                throw new Exception("رقم التسجيل موجود مسبقاً");
            }
            
            $stmt = $this->db->prepare("
                INSERT INTO employees (
                    registration_number, full_name, rank_position, gender, 
                    marital_status, children_count, children_names, children_levels,
                    salary, bank_account, hire_date
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $result = $stmt->execute([
                $data['registration_number'],
                $data['full_name'],
                $data['rank_position'],
                $data['gender'],
                $data['marital_status'],
                $data['children_count'] ?? 0,
                $data['children_names'] ?? '',
                $data['children_levels'] ?? '',
                $data['salary'],
                $data['bank_account'] ?? '',
                $data['hire_date']
            ]);
            
            if ($result) {
                return $this->db->lastInsertId();
            } else {
                throw new Exception("فشل في إضافة الموظف");
            }
        } catch (PDOException $e) {
            throw new Exception("خطأ في إضافة الموظف: " . $e->getMessage());
        }
    }
    
    public function updateEmployee($id, $data) {
        try {
            // التحقق من عدم تكرار رقم التسجيل (باستثناء الموظف الحالي)
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM employees WHERE registration_number = ? AND id != ?");
            $stmt->execute([$data['registration_number'], $id]);
            
            if ($stmt->fetchColumn() > 0) {
                throw new Exception("رقم التسجيل موجود مسبقاً");
            }
            
            $stmt = $this->db->prepare("
                UPDATE employees SET 
                    registration_number = ?, full_name = ?, rank_position = ?, 
                    gender = ?, marital_status = ?, children_count = ?, 
                    children_names = ?, children_levels = ?, salary = ?, 
                    bank_account = ?, hire_date = ?
                WHERE id = ?
            ");
            
            $result = $stmt->execute([
                $data['registration_number'],
                $data['full_name'],
                $data['rank_position'],
                $data['gender'],
                $data['marital_status'],
                $data['children_count'] ?? 0,
                $data['children_names'] ?? '',
                $data['children_levels'] ?? '',
                $data['salary'],
                $data['bank_account'] ?? '',
                $data['hire_date'],
                $id
            ]);
            
            if (!$result) {
                throw new Exception("فشل في تحديث بيانات الموظف");
            }
            
            return true;
        } catch (PDOException $e) {
            throw new Exception("خطأ في تحديث الموظف: " . $e->getMessage());
        }
    }
    
    public function deleteEmployee($id) {
        try {
            // التحقق من وجود سجلات مرتبطة بالموظف
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM expenses WHERE employee_id = ?");
            $stmt->execute([$id]);
            $expenseCount = $stmt->fetchColumn();
            
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM loans WHERE employee_id = ?");
            $stmt->execute([$id]);
            $loanCount = $stmt->fetchColumn();
            
            if ($expenseCount > 0 || $loanCount > 0) {
                throw new Exception("لا يمكن حذف الموظف لوجود سجلات مرتبطة به");
            }
            
            $stmt = $this->db->prepare("DELETE FROM employees WHERE id = ?");
            $result = $stmt->execute([$id]);
            
            if (!$result) {
                throw new Exception("فشل في حذف الموظف");
            }
            
            return true;
        } catch (PDOException $e) {
            throw new Exception("خطأ في حذف الموظف: " . $e->getMessage());
        }
    }
    
    public function searchEmployees($searchTerm, $filters = []) {
        try {
            $sql = "SELECT * FROM employees WHERE 1=1";
            $params = [];
            
            if (!empty($searchTerm)) {
                $sql .= " AND (full_name LIKE ? OR registration_number LIKE ?)";
                $params[] = "%$searchTerm%";
                $params[] = "%$searchTerm%";
            }
            
            if (!empty($filters['rank_position'])) {
                $sql .= " AND rank_position = ?";
                $params[] = $filters['rank_position'];
            }
            
            if (!empty($filters['marital_status'])) {
                $sql .= " AND marital_status = ?";
                $params[] = $filters['marital_status'];
            }
            
            if (!empty($filters['gender'])) {
                $sql .= " AND gender = ?";
                $params[] = $filters['gender'];
            }
            
            $sql .= " ORDER BY full_name ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في البحث: " . $e->getMessage());
        }
    }
    
    public function getEmployeeStatistics() {
        try {
            $stats = [];
            
            // إجمالي الموظفين
            $stmt = $this->db->query("SELECT COUNT(*) as total FROM employees");
            $stats['total'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // توزيع حسب الجنس
            $stmt = $this->db->query("
                SELECT gender, COUNT(*) as count 
                FROM employees 
                GROUP BY gender
            ");
            $stats['by_gender'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // توزيع حسب الحالة العائلية
            $stmt = $this->db->query("
                SELECT marital_status, COUNT(*) as count 
                FROM employees 
                GROUP BY marital_status
            ");
            $stats['by_marital_status'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // توزيع حسب الرتبة
            $stmt = $this->db->query("
                SELECT rank_position, COUNT(*) as count 
                FROM employees 
                GROUP BY rank_position
            ");
            $stats['by_rank'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // متوسط الراتب
            $stmt = $this->db->query("SELECT AVG(salary) as avg_salary FROM employees");
            $stats['avg_salary'] = $stmt->fetch(PDO::FETCH_ASSOC)['avg_salary'];
            
            return $stats;
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع الإحصائيات: " . $e->getMessage());
        }
    }
}

// معالجة الطلبات
try {
    $employeeManager = new EmployeeManager($pdo);
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    
    switch ($action) {
        case 'list':
            $employees = $employeeManager->getAllEmployees();
            echo json_encode([
                'success' => true,
                'data' => $employees
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'get':
            $id = $_GET['id'] ?? 0;
            if (!$id) {
                throw new Exception("معرف الموظف مطلوب");
            }
            
            $employee = $employeeManager->getEmployeeById($id);
            if (!$employee) {
                throw new Exception("الموظف غير موجود");
            }
            
            echo json_encode([
                'success' => true,
                'data' => $employee
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'create':
            $requiredFields = ['registration_number', 'full_name', 'rank_position', 'gender', 'marital_status', 'salary', 'hire_date'];
            
            foreach ($requiredFields as $field) {
                if (empty($_POST[$field])) {
                    throw new Exception("الحقل $field مطلوب");
                }
            }
            
            $employeeId = $employeeManager->createEmployee($_POST);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم إضافة الموظف بنجاح',
                'employee_id' => $employeeId
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'update':
            $id = $_POST['id'] ?? 0;
            if (!$id) {
                throw new Exception("معرف الموظف مطلوب");
            }
            
            $requiredFields = ['registration_number', 'full_name', 'rank_position', 'gender', 'marital_status', 'salary', 'hire_date'];
            
            foreach ($requiredFields as $field) {
                if (empty($_POST[$field])) {
                    throw new Exception("الحقل $field مطلوب");
                }
            }
            
            $employeeManager->updateEmployee($id, $_POST);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث بيانات الموظف بنجاح'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'delete':
            $id = $_POST['id'] ?? 0;
            if (!$id) {
                throw new Exception("معرف الموظف مطلوب");
            }
            
            $employeeManager->deleteEmployee($id);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم حذف الموظف بنجاح'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'search':
            $searchTerm = $_GET['search'] ?? '';
            $filters = [
                'rank_position' => $_GET['rank_position'] ?? '',
                'marital_status' => $_GET['marital_status'] ?? '',
                'gender' => $_GET['gender'] ?? ''
            ];
            
            $employees = $employeeManager->searchEmployees($searchTerm, $filters);
            
            echo json_encode([
                'success' => true,
                'data' => $employees
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'statistics':
            $stats = $employeeManager->getEmployeeStatistics();
            
            echo json_encode([
                'success' => true,
                'data' => $stats
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            throw new Exception("إجراء غير صحيح");
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
