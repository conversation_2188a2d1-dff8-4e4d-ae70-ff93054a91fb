# نظام إدارة لجنة الخدمات الاجتماعية - ولاية الجلفة

نظام شامل لإدارة أنشطة لجنة الخدمات الاجتماعية بولاية الجلفة، يشمل إدارة الموظفين، الإيرادات، المصروفات، السلفات المالية، العمرة، والاصطياف.

## المميزات الرئيسية

### 🏢 إدارة الموظفين
- تسجيل بيانات الموظفين الكاملة
- إدارة معلومات الأسرة والأطفال
- تتبع الرواتب وتواريخ التوظيف
- البحث والفلترة المتقدمة

### 💰 الإدارة المالية
- **الإيرادات**: تسجيل جميع مصادر الدخل (إعانات الدولة، فوائد بنكية، تبرعات)
- **المصروفات**: إدارة المساعدات الاجتماعية والمنح الدراسية
- **التقارير المالية**: تقارير شاملة وإحصائيات مفصلة

### 🏦 السلفات المالية
- طلبات السلفات مع نظام الموافقة
- حساب الأقساط الشهرية تلقائياً
- متابعة المدفوعات والأقساط المتأخرة
- التحقق من نسبة السلفة إلى الراتب

### 🕌 إدارة العمرة
- تسجيل طلبات العمرة
- نظام الأولوية حسب عدد مرات الاستفادة السابقة
- إجراء القرعة الإلكترونية
- إدارة التكاليف والمساهمات

### 🏖️ إدارة الاصطياف
- تسجيل طلبات الاصطياف العائلي
- إدارة الوجهات السياحية
- حساب التكاليف حسب عدد أفراد الأسرة
- تتبع المشاركين والأطفال

### 📊 التقارير والإحصائيات
- تقارير مالية شاملة
- إحصائيات المستفيدين
- رسوم بيانية تفاعلية
- تصدير البيانات (CSV, Excel)

## التقنيات المستخدمة

- **الواجهة الأمامية**: HTML5, CSS3, JavaScript
- **الواجهة الخلفية**: PHP 7.4+
- **قاعدة البيانات**: SQLite (محلية)
- **التصميم**: CSS Grid, Flexbox, Font Awesome
- **الرسوم البيانية**: Chart.js

## متطلبات التشغيل

- خادم ويب مع دعم PHP 7.4 أو أحدث
- SQLite مفعل في PHP
- متصفح ويب حديث

## التثبيت والتشغيل

### 1. تحميل الملفات
```bash
git clone [repository-url]
cd social-services-management
```

### 2. إعداد الخادم المحلي
```bash
# باستخدام PHP المدمج
php -S localhost:8000

# أو باستخدام XAMPP/WAMP
# انسخ الملفات إلى مجلد htdocs
```

### 3. الوصول للنظام
افتح المتصفح وانتقل إلى:
```
http://localhost:8000
```

### 4. بيانات الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## هيكل المشروع

```
social-services-management/
├── index.html              # صفحة تسجيل الدخول
├── dashboard.html          # لوحة التحكم الرئيسية
├── employees.html          # إدارة الموظفين
├── revenues.html           # إدارة الإيرادات
├── expenses.html           # إدارة المصروفات
├── loans.html              # إدارة السلفات المالية
├── umrah.html              # إدارة العمرة
├── vacation.html           # إدارة الاصطياف
├── reports.html            # التقارير والإحصائيات
├── settings.html           # إعدادات النظام
├── style.css               # ملف التنسيقات الرئيسي
├── database.php            # إعداد قاعدة البيانات
├── auth.php                # نظام المصادقة
├── employees.php           # API إدارة الموظفين
├── revenues.php            # API إدارة الإيرادات
├── expenses.php            # API إدارة المصروفات
├── loans.php               # API إدارة السلفات
├── umrah.php               # API إدارة العمرة
├── vacation.php            # API إدارة الاصطياف
├── dashboard_data.php      # API بيانات لوحة التحكم
└── social_services.db      # قاعدة البيانات (تُنشأ تلقائياً)
```

## البيانات التجريبية

يتضمن النظام بيانات تجريبية للاختبار:
- 5 موظفين نموذجيين
- إيرادات ومصروفات متنوعة
- أمثلة على المساعدات الاجتماعية

## الميزات الخاصة

### 🔒 الأمان
- تشفير كلمات المرور
- جلسات آمنة
- حماية من SQL Injection

### 🌐 دعم اللغة العربية
- واجهة مستخدم باللغة العربية بالكامل
- دعم الكتابة من اليمين إلى اليسار (RTL)
- تنسيق التواريخ والأرقام بالطريقة العربية

### 📱 التصميم المتجاوب
- يعمل على جميع أحجام الشاشات
- تصميم متوافق مع الهواتف والأجهزة اللوحية

### 💾 قاعدة البيانات المحلية
- لا يتطلب خادم قاعدة بيانات منفصل
- سهولة النسخ الاحتياطي والاستعادة
- أداء سريع للتطبيقات الصغيرة والمتوسطة

## الاستخدام

### إدارة الموظفين
1. انتقل إلى "إدارة الموظفين"
2. اضغط "إضافة موظف جديد"
3. املأ البيانات المطلوبة
4. احفظ البيانات

### إدارة السلفات
1. انتقل إلى "السلفات المالية"
2. اضغط "طلب سلفة جديدة"
3. اختر الموظف وحدد المبلغ
4. سيتم حساب الأقساط تلقائياً

### إنشاء التقارير
1. انتقل إلى "التقارير والإحصائيات"
2. اختر الفترة الزمنية
3. اضغط "إنشاء التقارير"
4. يمكن تصدير النتائج

## الدعم والصيانة

### النسخ الاحتياطي
- انسخ ملف `social_services.db` بانتظام
- استخدم ميزة النسخ الاحتياطي في الإعدادات

### التحديثات
- تحقق من التحديثات الجديدة
- اقرأ ملاحظات الإصدار قبل التحديث

## المساهمة في التطوير

نرحب بالمساهمات لتحسين النظام:
1. Fork المشروع
2. أنشئ فرع للميزة الجديدة
3. اكتب الكود مع التوثيق
4. أرسل Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## جهات الاتصال

- **المطور**: فريق التطوير
- **البريد الإلكتروني**: <EMAIL>
- **الدعم الفني**: متاح 24/7

---

**ملاحظة**: هذا النظام مصمم خصيصاً لاحتياجات لجنة الخدمات الاجتماعية بولاية الجلفة ويمكن تخصيصه حسب متطلبات المؤسسات الأخرى.
