<?php
require_once 'auth.php';

// التحقق من تسجيل الدخول
$auth->requireLogin();

header('Content-Type: application/json; charset=utf-8');

class VacationManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    public function getAllVacations() {
        try {
            $stmt = $this->db->query("
                SELECT v.*, e.full_name as employee_name, e.registration_number,
                       usr.full_name as created_by_name
                FROM vacation v
                LEFT JOIN employees e ON v.employee_id = e.id
                LEFT JOIN users usr ON v.created_by = usr.id
                ORDER BY v.created_at DESC
            ");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع بيانات طلبات الاصطياف: " . $e->getMessage());
        }
    }
    
    public function getVacationById($id) {
        try {
            $stmt = $this->db->prepare("
                SELECT v.*, e.full_name as employee_name, e.registration_number,
                       usr.full_name as created_by_name
                FROM vacation v
                LEFT JOIN employees e ON v.employee_id = e.id
                LEFT JOIN users usr ON v.created_by = usr.id
                WHERE v.id = ?
            ");
            $stmt->execute([$id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع بيانات طلب الاصطياف: " . $e->getMessage());
        }
    }
    
    public function createVacation($data, $userId) {
        try {
            // التحقق من عدم وجود طلب نشط للموظف
            $stmt = $this->db->prepare("
                SELECT COUNT(*) FROM vacation 
                WHERE employee_id = ? AND status IN ('قيد الدراسة', 'مقبول')
            ");
            $stmt->execute([$data['employee_id']]);
            
            if ($stmt->fetchColumn() > 0) {
                throw new Exception("يوجد طلب اصطياف نشط للموظف");
            }
            
            $stmt = $this->db->prepare("
                INSERT INTO vacation (
                    employee_id, family_members_count, children_participating, destination,
                    duration_days, total_cost, committee_contribution, employee_contribution,
                    travel_date, attachments, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $result = $stmt->execute([
                $data['employee_id'],
                $data['family_members_count'],
                $data['children_participating'] ?? 0,
                $data['destination'] ?? '',
                $data['duration_days'] ?? null,
                $data['total_cost'] ?? 0,
                $data['committee_contribution'] ?? 0,
                $data['employee_contribution'] ?? 0,
                $data['travel_date'] ?? null,
                $data['attachments'] ?? '',
                $userId
            ]);
            
            if ($result) {
                return $this->db->lastInsertId();
            } else {
                throw new Exception("فشل في إضافة طلب الاصطياف");
            }
        } catch (PDOException $e) {
            throw new Exception("خطأ في إضافة طلب الاصطياف: " . $e->getMessage());
        }
    }
    
    public function updateVacation($id, $data) {
        try {
            $stmt = $this->db->prepare("
                UPDATE vacation SET 
                    employee_id = ?, family_members_count = ?, children_participating = ?, 
                    destination = ?, duration_days = ?, total_cost = ?, 
                    committee_contribution = ?, employee_contribution = ?, 
                    travel_date = ?, attachments = ?
                WHERE id = ?
            ");
            
            $result = $stmt->execute([
                $data['employee_id'],
                $data['family_members_count'],
                $data['children_participating'] ?? 0,
                $data['destination'] ?? '',
                $data['duration_days'] ?? null,
                $data['total_cost'] ?? 0,
                $data['committee_contribution'] ?? 0,
                $data['employee_contribution'] ?? 0,
                $data['travel_date'] ?? null,
                $data['attachments'] ?? '',
                $id
            ]);
            
            if (!$result) {
                throw new Exception("فشل في تحديث طلب الاصطياف");
            }
            
            return true;
        } catch (PDOException $e) {
            throw new Exception("خطأ في تحديث طلب الاصطياف: " . $e->getMessage());
        }
    }
    
    public function updateVacationStatus($id, $status) {
        try {
            $stmt = $this->db->prepare("UPDATE vacation SET status = ? WHERE id = ?");
            $result = $stmt->execute([$status, $id]);
            
            if (!$result) {
                throw new Exception("فشل في تحديث حالة طلب الاصطياف");
            }
            
            return true;
        } catch (PDOException $e) {
            throw new Exception("خطأ في تحديث حالة طلب الاصطياف: " . $e->getMessage());
        }
    }
    
    public function deleteVacation($id) {
        try {
            $stmt = $this->db->prepare("DELETE FROM vacation WHERE id = ?");
            $result = $stmt->execute([$id]);
            
            if (!$result) {
                throw new Exception("فشل في حذف طلب الاصطياف");
            }
            
            return true;
        } catch (PDOException $e) {
            throw new Exception("خطأ في حذف طلب الاصطياف: " . $e->getMessage());
        }
    }
    
    public function getVacationStatistics() {
        try {
            $stats = [];
            
            // إجمالي الطلبات
            $stmt = $this->db->query("SELECT COUNT(*) as total FROM vacation");
            $stats['total'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // الطلبات المقبولة
            $stmt = $this->db->query("SELECT COUNT(*) as approved FROM vacation WHERE status = 'مقبول'");
            $stats['approved'] = $stmt->fetch(PDO::FETCH_ASSOC)['approved'];
            
            // الطلبات قيد الدراسة
            $stmt = $this->db->query("SELECT COUNT(*) as pending FROM vacation WHERE status = 'قيد الدراسة'");
            $stats['pending'] = $stmt->fetch(PDO::FETCH_ASSOC)['pending'];
            
            // إجمالي المشاركين
            $stmt = $this->db->query("
                SELECT COALESCE(SUM(family_members_count), 0) as total_participants 
                FROM vacation WHERE status = 'مقبول'
            ");
            $stats['total_participants'] = $stmt->fetch(PDO::FETCH_ASSOC)['total_participants'];
            
            // إجمالي الأطفال المشاركين
            $stmt = $this->db->query("
                SELECT COALESCE(SUM(children_participating), 0) as total_children 
                FROM vacation WHERE status = 'مقبول'
            ");
            $stats['total_children'] = $stmt->fetch(PDO::FETCH_ASSOC)['total_children'];
            
            // إجمالي التكلفة
            $stmt = $this->db->query("
                SELECT COALESCE(SUM(total_cost), 0) as total_cost 
                FROM vacation WHERE status = 'مقبول'
            ");
            $stats['total_cost'] = $stmt->fetch(PDO::FETCH_ASSOC)['total_cost'];
            
            // توزيع حسب الوجهة
            $stmt = $this->db->query("
                SELECT destination, COUNT(*) as count, COALESCE(SUM(family_members_count), 0) as participants
                FROM vacation 
                WHERE destination IS NOT NULL AND destination != ''
                GROUP BY destination
                ORDER BY count DESC
            ");
            $stats['by_destination'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // توزيع حسب الحالة
            $stmt = $this->db->query("
                SELECT status, COUNT(*) as count
                FROM vacation 
                GROUP BY status
            ");
            $stats['by_status'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // الطلبات الشهرية (آخر 12 شهر)
            $monthlyVacations = [];
            for ($i = 11; $i >= 0; $i--) {
                $month = date('Y-m', strtotime("-$i months"));
                $stmt = $this->db->prepare("
                    SELECT COUNT(*) as count, COALESCE(SUM(family_members_count), 0) as participants
                    FROM vacation 
                    WHERE strftime('%Y-%m', created_at) = ?
                ");
                $stmt->execute([$month]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $monthlyVacations[] = [
                    'month' => $month,
                    'count' => intval($result['count']),
                    'participants' => intval($result['participants'])
                ];
            }
            $stats['monthly_vacations'] = $monthlyVacations;
            
            return $stats;
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع الإحصائيات: " . $e->getMessage());
        }
    }
    
    public function getVacationsByDestination($destination) {
        try {
            $stmt = $this->db->prepare("
                SELECT v.*, e.full_name as employee_name, e.registration_number
                FROM vacation v
                LEFT JOIN employees e ON v.employee_id = e.id
                WHERE v.destination = ?
                ORDER BY v.created_at DESC
            ");
            $stmt->execute([$destination]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع طلبات الاصطياف: " . $e->getMessage());
        }
    }
    
    public function getVacationsByDateRange($startDate, $endDate) {
        try {
            $stmt = $this->db->prepare("
                SELECT v.*, e.full_name as employee_name, e.registration_number
                FROM vacation v
                LEFT JOIN employees e ON v.employee_id = e.id
                WHERE v.travel_date BETWEEN ? AND ?
                ORDER BY v.travel_date ASC
            ");
            $stmt->execute([$startDate, $endDate]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع طلبات الاصطياف: " . $e->getMessage());
        }
    }
    
    public function getPopularDestinations() {
        try {
            $stmt = $this->db->query("
                SELECT destination, COUNT(*) as request_count, 
                       COALESCE(SUM(family_members_count), 0) as total_participants,
                       COALESCE(AVG(total_cost), 0) as avg_cost
                FROM vacation 
                WHERE destination IS NOT NULL AND destination != ''
                GROUP BY destination
                ORDER BY request_count DESC
                LIMIT 10
            ");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("خطأ في استرجاع الوجهات الشائعة: " . $e->getMessage());
        }
    }
    
    public function getVacationReport($year) {
        try {
            $report = [];
            
            // إحصائيات السنة
            $stmt = $this->db->prepare("
                SELECT 
                    COUNT(*) as total_requests,
                    COALESCE(SUM(family_members_count), 0) as total_participants,
                    COALESCE(SUM(children_participating), 0) as total_children,
                    COALESCE(SUM(total_cost), 0) as total_cost,
                    COALESCE(SUM(committee_contribution), 0) as committee_contribution,
                    COALESCE(SUM(employee_contribution), 0) as employee_contribution
                FROM vacation 
                WHERE strftime('%Y', created_at) = ?
            ");
            $stmt->execute([$year]);
            $report['summary'] = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // التوزيع الشهري
            $stmt = $this->db->prepare("
                SELECT 
                    strftime('%m', created_at) as month,
                    COUNT(*) as requests,
                    COALESCE(SUM(family_members_count), 0) as participants
                FROM vacation 
                WHERE strftime('%Y', created_at) = ?
                GROUP BY strftime('%m', created_at)
                ORDER BY month ASC
            ");
            $stmt->execute([$year]);
            $report['monthly'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // التوزيع حسب الوجهة
            $stmt = $this->db->prepare("
                SELECT 
                    destination,
                    COUNT(*) as requests,
                    COALESCE(SUM(family_members_count), 0) as participants,
                    COALESCE(SUM(total_cost), 0) as total_cost
                FROM vacation 
                WHERE strftime('%Y', created_at) = ? 
                AND destination IS NOT NULL AND destination != ''
                GROUP BY destination
                ORDER BY requests DESC
            ");
            $stmt->execute([$year]);
            $report['by_destination'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return $report;
        } catch (PDOException $e) {
            throw new Exception("خطأ في إنشاء التقرير: " . $e->getMessage());
        }
    }
}

// معالجة الطلبات
try {
    $vacationManager = new VacationManager($pdo);
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    $currentUser = $auth->getCurrentUser();
    
    switch ($action) {
        case 'list':
            $vacations = $vacationManager->getAllVacations();
            echo json_encode([
                'success' => true,
                'data' => $vacations
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'get':
            $id = $_GET['id'] ?? 0;
            if (!$id) {
                throw new Exception("معرف طلب الاصطياف مطلوب");
            }
            
            $vacation = $vacationManager->getVacationById($id);
            if (!$vacation) {
                throw new Exception("طلب الاصطياف غير موجود");
            }
            
            echo json_encode([
                'success' => true,
                'data' => $vacation
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'create':
            $requiredFields = ['employee_id', 'family_members_count'];
            
            foreach ($requiredFields as $field) {
                if (empty($_POST[$field])) {
                    throw new Exception("الحقل $field مطلوب");
                }
            }
            
            $vacationId = $vacationManager->createVacation($_POST, $currentUser['id']);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم إضافة طلب الاصطياف بنجاح',
                'vacation_id' => $vacationId
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'update':
            $id = $_POST['id'] ?? 0;
            if (!$id) {
                throw new Exception("معرف طلب الاصطياف مطلوب");
            }
            
            $vacationManager->updateVacation($id, $_POST);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث طلب الاصطياف بنجاح'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'update_status':
            $id = $_POST['id'] ?? 0;
            $status = $_POST['status'] ?? '';
            
            if (!$id || !$status) {
                throw new Exception("معرف طلب الاصطياف والحالة مطلوبان");
            }
            
            $vacationManager->updateVacationStatus($id, $status);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث حالة طلب الاصطياف بنجاح'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'delete':
            $id = $_POST['id'] ?? 0;
            if (!$id) {
                throw new Exception("معرف طلب الاصطياف مطلوب");
            }
            
            $vacationManager->deleteVacation($id);
            
            echo json_encode([
                'success' => true,
                'message' => 'تم حذف طلب الاصطياف بنجاح'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'statistics':
            $stats = $vacationManager->getVacationStatistics();
            
            echo json_encode([
                'success' => true,
                'data' => $stats
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'by_destination':
            $destination = $_GET['destination'] ?? '';
            if (!$destination) {
                throw new Exception("الوجهة مطلوبة");
            }
            
            $vacations = $vacationManager->getVacationsByDestination($destination);
            
            echo json_encode([
                'success' => true,
                'data' => $vacations
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'popular_destinations':
            $destinations = $vacationManager->getPopularDestinations();
            
            echo json_encode([
                'success' => true,
                'data' => $destinations
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'annual_report':
            $year = $_GET['year'] ?? date('Y');
            $report = $vacationManager->getVacationReport($year);
            
            echo json_encode([
                'success' => true,
                'data' => $report
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            throw new Exception("إجراء غير صحيح");
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
