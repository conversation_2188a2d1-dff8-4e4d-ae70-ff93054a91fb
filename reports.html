<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير والإحصائيات - لجنة الخدمات الاجتماعية</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="dashboard">
    <!-- شريط التنقل العلوي -->
    <nav class="navbar">
        <div class="navbar-content">
            <a href="dashboard.html" class="navbar-brand">
                <i class="fas fa-users"></i>
                لجنة الخدمات الاجتماعية - ولاية الجلفة
            </a>
            <div class="navbar-user">
                <div class="user-info">
                    <i class="fas fa-user"></i>
                    <span id="userName">مرحباً، مدير النظام</span>
                </div>
                <button class="btn btn-logout" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>
    </nav>

    <!-- الشريط الجانبي -->
    <aside class="sidebar">
        <ul class="sidebar-menu">
            <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i>لوحة التحكم</a></li>
            <li><a href="employees.html"><i class="fas fa-users"></i>إدارة الموظفين</a></li>
            <li><a href="revenues.html"><i class="fas fa-arrow-up"></i>الإيرادات</a></li>
            <li><a href="expenses.html"><i class="fas fa-arrow-down"></i>المصروفات</a></li>
            <li><a href="loans.html"><i class="fas fa-money-bill-wave"></i>السلفات المالية</a></li>
            <li><a href="umrah.html"><i class="fas fa-kaaba"></i>العمرة</a></li>
            <li><a href="vacation.html"><i class="fas fa-umbrella-beach"></i>الاصطياف</a></li>
            <li><a href="reports.html" class="active"><i class="fas fa-chart-bar"></i>التقارير والإحصائيات</a></li>
            <li><a href="settings.html"><i class="fas fa-cog"></i>الإعدادات</a></li>
        </ul>
    </aside>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">التقارير والإحصائيات</h1>
            <p class="page-subtitle">تقارير شاملة عن أنشطة لجنة الخدمات الاجتماعية</p>
        </div>

        <!-- فلاتر التقارير -->
        <div class="card" style="margin-bottom: 30px;">
            <div class="card-header">
                <h3 class="card-title">فلاتر التقارير</h3>
            </div>
            <div class="card-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="reportYear">السنة</label>
                        <select id="reportYear" class="form-control">
                            <option value="2024">2024</option>
                            <option value="2023">2023</option>
                            <option value="2022">2022</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="reportMonth">الشهر</label>
                        <select id="reportMonth" class="form-control">
                            <option value="">جميع الأشهر</option>
                            <option value="01">يناير</option>
                            <option value="02">فبراير</option>
                            <option value="03">مارس</option>
                            <option value="04">أبريل</option>
                            <option value="05">مايو</option>
                            <option value="06">يونيو</option>
                            <option value="07">يوليو</option>
                            <option value="08">أغسطس</option>
                            <option value="09">سبتمبر</option>
                            <option value="10">أكتوبر</option>
                            <option value="11">نوفمبر</option>
                            <option value="12">ديسمبر</option>
                        </select>
                    </div>
                    <div class="form-group" style="display: flex; align-items: end;">
                        <button class="btn btn-primary" onclick="generateReports()">
                            <i class="fas fa-chart-line"></i>
                            إنشاء التقارير
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإحصائيات العامة -->
        <div class="stats-grid" style="margin-bottom: 30px;">
            <div class="stat-card">
                <div class="stat-icon green">
                    <i class="fas fa-arrow-up"></i>
                </div>
                <div class="stat-number currency" id="totalRevenuesReport">0</div>
                <div class="stat-label">إجمالي الإيرادات</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon orange">
                    <i class="fas fa-arrow-down"></i>
                </div>
                <div class="stat-number currency" id="totalExpensesReport">0</div>
                <div class="stat-label">إجمالي المصروفات</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon blue">
                    <i class="fas fa-balance-scale"></i>
                </div>
                <div class="stat-number currency" id="netBalanceReport">0</div>
                <div class="stat-label">الرصيد الصافي</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon purple">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number" id="beneficiariesCount">0</div>
                <div class="stat-label">عدد المستفيدين</div>
            </div>
        </div>

        <!-- الرسوم البيانية -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
            <!-- رسم بياني للإيرادات والمصروفات الشهرية -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">الإيرادات والمصروفات الشهرية</h3>
                </div>
                <div class="card-body">
                    <canvas id="monthlyFinancialChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- رسم بياني لتوزيع المصروفات -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">توزيع المصروفات حسب الفئة</h3>
                </div>
                <div class="card-body">
                    <canvas id="expenseDistributionChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- تقارير مفصلة -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
            <!-- تقرير السلفات -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">تقرير السلفات المالية</h3>
                </div>
                <div class="card-body">
                    <div class="report-item">
                        <span>إجمالي السلفات:</span>
                        <span id="totalLoansReport">0</span>
                    </div>
                    <div class="report-item">
                        <span>السلفات النشطة:</span>
                        <span id="activeLoansReport">0</span>
                    </div>
                    <div class="report-item">
                        <span>المبلغ الإجمالي:</span>
                        <span class="currency" id="totalLoanAmountReport">0</span>
                    </div>
                    <div class="report-item">
                        <span>الأقساط المتأخرة:</span>
                        <span id="overdueInstallmentsReport">0</span>
                    </div>
                </div>
            </div>

            <!-- تقرير العمرة والاصطياف -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">تقرير العمرة والاصطياف</h3>
                </div>
                <div class="card-body">
                    <div class="report-item">
                        <span>طلبات العمرة:</span>
                        <span id="umrahApplicationsReport">0</span>
                    </div>
                    <div class="report-item">
                        <span>طلبات الاصطياف:</span>
                        <span id="vacationApplicationsReport">0</span>
                    </div>
                    <div class="report-item">
                        <span>المشاركين في الاصطياف:</span>
                        <span id="vacationParticipantsReport">0</span>
                    </div>
                    <div class="report-item">
                        <span>تكلفة العمرة:</span>
                        <span class="currency" id="umrahCostReport">0</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول التقارير التفصيلية -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">التقارير التفصيلية</h3>
                <div>
                    <button class="btn btn-success btn-sm" onclick="exportReport('excel')">
                        <i class="fas fa-file-excel"></i>
                        تصدير Excel
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="exportReport('pdf')">
                        <i class="fas fa-file-pdf"></i>
                        تصدير PDF
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>الشهر</th>
                                <th>الإيرادات</th>
                                <th>المصروفات</th>
                                <th>الرصيد</th>
                                <th>عدد المعاملات</th>
                            </tr>
                        </thead>
                        <tbody id="detailedReportTable">
                            <tr>
                                <td colspan="5" style="text-align: center; padding: 40px;">
                                    <div class="loading">
                                        <div class="spinner"></div>
                                        <p>جاري تحميل التقرير...</p>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <script>
        let reportData = {};

        // تحميل البيانات عند تحميل الصفحة
        window.addEventListener('load', function() {
            // تعيين السنة الحالية
            document.getElementById('reportYear').value = new Date().getFullYear();
            generateReports();
        });

        // إنشاء التقارير
        async function generateReports() {
            const year = document.getElementById('reportYear').value;
            const month = document.getElementById('reportMonth').value;
            
            try {
                // تحميل البيانات من جميع المصادر
                const [revenueData, expenseData, loanData, umrahData, vacationData] = await Promise.all([
                    fetch(`revenues.php?action=statistics`).then(r => r.json()),
                    fetch(`expenses.php?action=statistics`).then(r => r.json()),
                    fetch(`loans.php?action=statistics`).then(r => r.json()),
                    fetch(`umrah.php?action=statistics`).then(r => r.json()),
                    fetch(`vacation.php?action=statistics`).then(r => r.json())
                ]);

                if (revenueData.success && expenseData.success && loanData.success) {
                    reportData = {
                        revenues: revenueData.data,
                        expenses: expenseData.data,
                        loans: loanData.data,
                        umrah: umrahData.success ? umrahData.data : {},
                        vacation: vacationData.success ? vacationData.data : {}
                    };

                    updateReportStatistics();
                    updateCharts();
                    updateDetailedReport();
                }
            } catch (error) {
                console.error('خطأ في تحميل بيانات التقارير:', error);
                showAlert('حدث خطأ في تحميل البيانات', 'danger');
            }
        }

        // تحديث الإحصائيات
        function updateReportStatistics() {
            const totalRevenues = reportData.revenues.total || 0;
            const totalExpenses = reportData.expenses.total || 0;
            const netBalance = totalRevenues - totalExpenses;

            document.getElementById('totalRevenuesReport').textContent = formatCurrency(totalRevenues);
            document.getElementById('totalExpensesReport').textContent = formatCurrency(totalExpenses);
            document.getElementById('netBalanceReport').textContent = formatCurrency(netBalance);
            
            // تغيير لون الرصيد حسب القيمة
            const balanceElement = document.getElementById('netBalanceReport');
            if (netBalance >= 0) {
                balanceElement.style.color = '#28a745';
            } else {
                balanceElement.style.color = '#dc3545';
            }

            // إحصائيات أخرى
            document.getElementById('totalLoansReport').textContent = reportData.loans.total || 0;
            document.getElementById('activeLoansReport').textContent = reportData.loans.approved || 0;
            document.getElementById('totalLoanAmountReport').textContent = formatCurrency(reportData.loans.total_amount || 0);
            document.getElementById('overdueInstallmentsReport').textContent = reportData.loans.overdue || 0;

            document.getElementById('umrahApplicationsReport').textContent = reportData.umrah.total || 0;
            document.getElementById('vacationApplicationsReport').textContent = reportData.vacation.total || 0;
            document.getElementById('vacationParticipantsReport').textContent = reportData.vacation.total_participants || 0;
            document.getElementById('umrahCostReport').textContent = formatCurrency(reportData.umrah.total_cost || 0);

            // حساب عدد المستفيدين
            const beneficiaries = (reportData.loans.approved || 0) + (reportData.umrah.approved || 0) + (reportData.vacation.approved || 0);
            document.getElementById('beneficiariesCount').textContent = beneficiaries;
        }

        // تحديث الرسوم البيانية
        function updateCharts() {
            // رسم بياني للإيرادات والمصروفات الشهرية
            const ctx1 = document.getElementById('monthlyFinancialChart').getContext('2d');
            new Chart(ctx1, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                    datasets: [{
                        label: 'الإيرادات',
                        data: reportData.revenues.monthly_revenues?.map(m => m.total) || Array(12).fill(0),
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'المصروفات',
                        data: reportData.expenses.monthly_expenses?.map(m => m.total) || Array(12).fill(0),
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // رسم بياني لتوزيع المصروفات
            const ctx2 = document.getElementById('expenseDistributionChart').getContext('2d');
            const expenseCategories = reportData.expenses.by_category || [];
            
            new Chart(ctx2, {
                type: 'doughnut',
                data: {
                    labels: expenseCategories.map(cat => cat.category),
                    datasets: [{
                        data: expenseCategories.map(cat => cat.total),
                        backgroundColor: [
                            '#667eea',
                            '#56ab2f',
                            '#f093fb',
                            '#4facfe',
                            '#ff6b6b',
                            '#feca57'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        }
                    }
                }
            });
        }

        // تحديث التقرير التفصيلي
        function updateDetailedReport() {
            const tableBody = document.getElementById('detailedReportTable');
            const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
            
            const monthlyRevenues = reportData.revenues.monthly_revenues || [];
            const monthlyExpenses = reportData.expenses.monthly_expenses || [];
            
            let tableHTML = '';
            
            for (let i = 0; i < 12; i++) {
                const revenue = monthlyRevenues[i]?.total || 0;
                const expense = monthlyExpenses[i]?.total || 0;
                const balance = revenue - expense;
                const transactions = (monthlyRevenues[i]?.count || 0) + (monthlyExpenses[i]?.count || 0);
                
                tableHTML += `
                    <tr>
                        <td>${months[i]}</td>
                        <td class="currency">${formatCurrency(revenue)}</td>
                        <td class="currency">${formatCurrency(expense)}</td>
                        <td class="currency" style="color: ${balance >= 0 ? '#28a745' : '#dc3545'}">${formatCurrency(balance)}</td>
                        <td>${transactions}</td>
                    </tr>
                `;
            }
            
            tableBody.innerHTML = tableHTML;
        }

        // تصدير التقرير
        function exportReport(format) {
            if (format === 'excel') {
                // تحويل البيانات إلى CSV
                const headers = ['الشهر', 'الإيرادات', 'المصروفات', 'الرصيد', 'عدد المعاملات'];
                const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                
                let csvContent = headers.join(',') + '\n';
                
                for (let i = 0; i < 12; i++) {
                    const revenue = reportData.revenues.monthly_revenues?.[i]?.total || 0;
                    const expense = reportData.expenses.monthly_expenses?.[i]?.total || 0;
                    const balance = revenue - expense;
                    const transactions = (reportData.revenues.monthly_revenues?.[i]?.count || 0) + (reportData.expenses.monthly_expenses?.[i]?.count || 0);
                    
                    csvContent += `${months[i]},${revenue},${expense},${balance},${transactions}\n`;
                }
                
                // تحميل الملف
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = `financial_report_${new Date().toISOString().split('T')[0]}.csv`;
                link.click();
                
                showAlert('تم تصدير التقرير بنجاح', 'success');
            } else if (format === 'pdf') {
                showAlert('ميزة تصدير PDF ستكون متاحة قريباً', 'info');
            }
        }

        // وظائف مساعدة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-DZ', {
                style: 'decimal',
                minimumFractionDigits: 2
            }).format(amount);
        }

        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alert.style.position = 'fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.style.minWidth = '300px';
            
            document.body.appendChild(alert);
            
            setTimeout(() => {
                document.body.removeChild(alert);
            }, 5000);
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                window.location.href = 'index.html';
            }
        }
    </script>

    <style>
        .report-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .report-item:last-child {
            border-bottom: none;
        }
        
        .report-item span:last-child {
            font-weight: bold;
            color: #333;
        }
    </style>
</body>
</html>
